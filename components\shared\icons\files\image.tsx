export default function ImageFileIcon({
  className,
  isLight = true,
}: {
  className?: string;
  isLight?: boolean;
}) {
  return (
    <svg
      width="576"
      height="576"
      viewBox="0 0 576 576"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#a)">
        <rect
          x="57"
          y="59"
          width="454"
          height="462"
          fill={isLight ? "#000" : "#fff"}
        />
        <path
          opacity="0.999"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M497.5 31.5H77.5C53.1667 37.8333 37.8333 53.1667 31.5 77.5V497.5C37.8333 521.833 53.1667 537.167 77.5 543.5H497.5C521.833 537.167 537.167 521.833 543.5 497.5V77.5C537.167 53.1667 521.833 37.8333 497.5 31.5ZM147 165.889C147 155.457 155.457 147 165.889 147H410.111C420.543 147 429 155.457 429 165.889V301.706L402.48 275.186L402.478 275.185C392.935 265.644 379.994 260.285 366.5 260.285C353.006 260.285 340.065 265.644 330.522 275.185L330.52 275.186L176.706 429H165.889C155.457 429 147 420.543 147 410.111V165.889ZM183.412 461C183.359 461 183.307 461 183.255 461H165.889C137.784 461 115 438.216 115 410.111V165.889C115 137.784 137.784 115 165.889 115H410.111C438.216 115 461 137.784 461 165.889V340.318C461 340.328 461 340.339 461 340.349V410.111C461 438.216 438.216 461 410.111 461H183.412ZM429 346.961V410.111C429 420.543 420.543 429 410.111 429H221.961L353.146 297.815L353.147 297.814C356.689 294.274 361.492 292.285 366.5 292.285C371.508 292.285 376.311 294.274 379.853 297.814L379.854 297.815L429 346.961ZM235.667 216.778C225.235 216.778 216.778 225.235 216.778 235.667C216.778 246.099 225.235 254.556 235.667 254.556C246.099 254.556 254.555 246.099 254.555 235.667C254.555 225.235 246.099 216.778 235.667 216.778ZM184.778 235.667C184.778 207.562 207.561 184.778 235.667 184.778C263.772 184.778 286.555 207.562 286.555 235.667C286.555 263.772 263.772 286.556 235.667 286.556C207.561 286.556 184.778 263.772 184.778 235.667Z"
          fill={isLight ? "#fff" : "#111827"}
        />
      </g>
      <rect
        x="16"
        y="16"
        width="544"
        height="544"
        rx="48"
        stroke={isLight ? "#000" : "#fff"}
        strokeWidth="32"
      />
      <defs>
        <clipPath id="a">
          <rect
            x="32"
            y="32"
            width="512"
            height="512"
            rx="32"
            fill={isLight ? "#fff" : "#111827"}
          />
        </clipPath>
      </defs>
    </svg>
  );
}
