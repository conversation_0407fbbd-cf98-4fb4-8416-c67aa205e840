import { NextApiRequest, NextApiResponse } from "next";
import { getServerSession } from "next-auth/next";
import { ApiAuthType } from "@prisma/client";

import { authOptions } from "@/pages/api/auth/[...nextauth]";
import { 
  createApiSpecification,
  listApiSpecifications,
  ApiSpecificationData,
} from "@/lib/api/api-specifications/create-api-specification";
import { errorhandler } from "@/lib/errorHandler";
import { CustomUser } from "@/lib/types";
import { getTeamWithUsersAndDocument } from "@/lib/team/helper";

export default async function handle(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method === "GET") {
    // GET /api/teams/:teamId/api-specifications
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).end("Unauthorized");
    }

    const { teamId } = req.query as { teamId: string };
    const userId = (session.user as CustomUser).id;

    try {
      // Verify user is part of the team
      await getTeamWithUsersAndDocument({
        teamId,
        userId,
      });

      const apiSpecifications = await listApiSpecifications(teamId);
      return res.status(200).json(apiSpecifications);
    } catch (error) {
      errorhandler(error, res);
    }
  } else if (req.method === "POST") {
    // POST /api/teams/:teamId/api-specifications
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).end("Unauthorized");
    }

    const { teamId } = req.query as { teamId: string };
    const userId = (session.user as CustomUser).id;

    const {
      documentId,
      name,
      description,
      baseUrl,
      apiVersion,
      authType,
      authConfig,
      parsedSchema,
      functionTools,
    } = req.body as {
      documentId: string;
      name: string;
      description?: string;
      baseUrl?: string;
      apiVersion?: string;
      authType?: ApiAuthType;
      authConfig?: any;
      parsedSchema?: any;
      functionTools?: Array<{
        name: string;
        description?: string;
        endpoint: string;
        method: string;
        parameters?: any;
        responseSchema?: any;
        isEnabled?: boolean;
      }>;
    };

    if (!documentId || !name) {
      return res.status(400).json({ 
        error: "documentId and name are required" 
      });
    }

    try {
      // Verify user is part of the team
      await getTeamWithUsersAndDocument({
        teamId,
        userId,
      });

      const apiSpecData: ApiSpecificationData = {
        name,
        description,
        baseUrl,
        apiVersion,
        authType,
        authConfig,
        parsedSchema,
        functionTools,
      };

      const apiSpecification = await createApiSpecification({
        documentId,
        apiSpecData,
        teamId,
        ownerId: userId,
      });

      return res.status(201).json(apiSpecification);
    } catch (error) {
      errorhandler(error, res);
    }
  } else {
    // We only allow GET and POST requests
    res.setHeader("Allow", ["GET", "POST"]);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
