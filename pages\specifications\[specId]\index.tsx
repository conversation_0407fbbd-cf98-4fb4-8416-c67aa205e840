import { useRouter } from "next/router";
import { useState } from "react";

import { useTeam } from "@/context/team-context";
import { 
  CodeIcon, 
  EditIcon, 
  TrashIcon, 
  DownloadIcon,
  ExternalLinkIcon,
  SettingsIcon,
  GlobeIcon,
  UserIcon,
  CalendarIcon,
} from "lucide-react";
import useS<PERSON> from "swr";
import { toast } from "sonner";

import AppLayout from "@/components/layouts/app";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import LoadingSpinner from "@/components/ui/loading-spinner";

import { ApiSpecViewer } from "@/components/api-specifications/api-spec-viewer";
import { FunctionToolsList } from "@/components/api-specifications/function-tools-list";

import { fetcher, timeAgo } from "@/lib/utils";

export default function ApiSpecificationDetailPage() {
  const router = useRouter();
  const { specId } = router.query;
  const teamInfo = useTeam();
  const [deleting, setDeleting] = useState(false);

  const teamId = teamInfo?.currentTeam?.id;

  const {
    data: apiSpecification,
    error,
    mutate,
  } = useSWR(
    teamId && specId ? `/api/teams/${teamId}/api-specifications/${specId}` : null,
    fetcher,
    {
      revalidateOnFocus: false,
    }
  );

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this API specification?")) {
      return;
    }

    setDeleting(true);
    try {
      const response = await fetch(
        `/api/teams/${teamId}/api-specifications/${specId}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to delete API specification");
      }

      toast.success("API specification deleted successfully");
      router.push("/specifications");
    } catch (error) {
      console.error("Error deleting API specification:", error);
      toast.error("Failed to delete API specification");
    } finally {
      setDeleting(false);
    }
  };

  const handleDownload = () => {
    if (!apiSpecification?.parsedSchema) {
      toast.error("No specification data available for download");
      return;
    }

    const dataStr = JSON.stringify(apiSpecification.parsedSchema, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${apiSpecification.name.replace(/[^a-zA-Z0-9]/g, "_")}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  if (error) {
    return (
      <AppLayout>
        <div className="flex h-screen items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-semibold">Error loading API specification</h2>
            <p className="text-muted-foreground">
              {error.message || "Something went wrong"}
            </p>
            <Button
              onClick={() => router.push("/specifications")}
              className="mt-4"
            >
              Back to API Specifications
            </Button>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (!apiSpecification) {
    return (
      <AppLayout>
        <div className="flex h-screen items-center justify-center">
          <LoadingSpinner className="h-8 w-8" />
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <main className="relative mx-2 mb-10 mt-4 space-y-8 overflow-hidden px-1 sm:mx-3 md:mx-5 md:mt-5 lg:mx-7 lg:mt-8 xl:mx-10">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <CodeIcon className="h-6 w-6 text-blue-600" />
              <h1 className="text-3xl font-bold tracking-tight">
                {apiSpecification.name}
              </h1>
            </div>
            {apiSpecification.description && (
              <p className="text-lg text-muted-foreground">
                {apiSpecification.description}
              </p>
            )}
            
            {/* Metadata */}
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
              {apiSpecification.baseUrl && (
                <div className="flex items-center gap-1">
                  <GlobeIcon className="h-4 w-4" />
                  <span>{apiSpecification.baseUrl}</span>
                </div>
              )}
              {apiSpecification.apiVersion && (
                <Badge variant="secondary">v{apiSpecification.apiVersion}</Badge>
              )}
              {apiSpecification.authType && (
                <Badge variant="outline">{apiSpecification.authType}</Badge>
              )}
              {apiSpecification.owner && (
                <div className="flex items-center gap-1">
                  <UserIcon className="h-4 w-4" />
                  <span>{apiSpecification.owner.name || apiSpecification.owner.email}</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <CalendarIcon className="h-4 w-4" />
                <span>Created {timeAgo(apiSpecification.createdAt)}</span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownload}
            >
              <DownloadIcon className="h-4 w-4 mr-2" />
              Download
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/specifications/${specId}/edit`)}
            >
              <EditIcon className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDelete}
              disabled={deleting}
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              {deleting ? "Deleting..." : "Delete"}
            </Button>
          </div>
        </div>

        <Separator />

        {/* Content Tabs */}
        <Tabs defaultValue="preview" className="w-full">
          <TabsList>
            <TabsTrigger value="preview">API Preview</TabsTrigger>
            <TabsTrigger value="functions">
              Function Tools ({apiSpecification.functionTools?.length || 0})
            </TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
          </TabsList>

          <TabsContent value="preview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>API Documentation</CardTitle>
                <CardDescription>
                  Interactive API documentation powered by Stoplight Elements
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ApiSpecViewer 
                  specification={apiSpecification.parsedSchema}
                  baseUrl={apiSpecification.baseUrl}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="functions" className="space-y-4">
            <FunctionToolsList
              functionTools={apiSpecification.functionTools || []}
              apiSpecificationId={apiSpecification.id}
              teamId={teamId!}
              onUpdate={mutate}
            />
          </TabsContent>

          <TabsContent value="details" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>API Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Name</label>
                    <p className="text-sm text-muted-foreground">{apiSpecification.name}</p>
                  </div>
                  {apiSpecification.description && (
                    <div>
                      <label className="text-sm font-medium">Description</label>
                      <p className="text-sm text-muted-foreground">{apiSpecification.description}</p>
                    </div>
                  )}
                  {apiSpecification.baseUrl && (
                    <div>
                      <label className="text-sm font-medium">Base URL</label>
                      <p className="text-sm text-muted-foreground">{apiSpecification.baseUrl}</p>
                    </div>
                  )}
                  {apiSpecification.apiVersion && (
                    <div>
                      <label className="text-sm font-medium">Version</label>
                      <p className="text-sm text-muted-foreground">{apiSpecification.apiVersion}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Authentication</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Type</label>
                    <p className="text-sm text-muted-foreground">
                      {apiSpecification.authType || "None"}
                    </p>
                  </div>
                  {apiSpecification.authConfig && (
                    <div>
                      <label className="text-sm font-medium">Configuration</label>
                      <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                        {JSON.stringify(apiSpecification.authConfig, null, 2)}
                      </pre>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </AppLayout>
  );
}
