VERSION 1

DESCRIPTION >
    Event track when a visitor clicks a link

SCHEMA >
    `timestamp` DateTime64(3) `json:$.timestamp`,
    `click_id` String `json:$.click_id`,
    `view_id` String `json:$.view_id`,
    `link_id` String `json:$.link_id`,
    `document_id` Nullable(String) `json:$.document_id`,
    `dataroom_id` Nullable(String) `json:$.dataroom_id`,
    `continent` LowCardinality(String) `json:$.continent`,
    `country` LowCardinality(String) `json:$.country`,
    `city` String `json:$.city`,
    `region` String `json:$.region`,
    `latitude` String `json:$.latitude`,
    `longitude` String `json:$.longitude`,
    `device` LowCardinality(String) `json:$.device`,
    `device_model` LowCardinality(String) `json:$.device_model`,
    `device_vendor` LowCardinality(String) `json:$.device_vendor`,
    `browser` LowCardinality(String) `json:$.browser`,
    `browser_version` String `json:$.browser_version`,
    `os` LowCardinality(String) `json:$.os`,
    `os_version` String `json:$.os_version`,
    `engine` LowCardinality(String) `json:$.engine`,
    `engine_version` String `json:$.engine_version`,
    `cpu_architecture` LowCardinality(String) `json:$.cpu_architecture`,
    `ua` String `json:$.ua`,
    `bot` UInt8 `json:$.bot`,
    `referer` String `json:$.referer`,
    `referer_url` String `json:$.referer_url`,
    `ip_address` Nullable(String) `json:$.ip_address`

ENGINE "MergeTree"
ENGINE_PARTITION_KEY "toYYYYMM(timestamp)"
ENGINE_SORTING_KEY "timestamp, view_id, link_id, click_id"
