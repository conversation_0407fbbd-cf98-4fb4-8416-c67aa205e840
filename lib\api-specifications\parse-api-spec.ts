import { ApiAuthType } from "@prisma/client";
import yaml from "js-yaml";

interface ParsedApiSpec {
  schema: any;
  baseUrl?: string;
  version?: string;
  authType?: ApiAuthType;
  authConfig?: any;
  functionTools: Array<{
    name: string;
    description?: string;
    endpoint: string;
    method: string;
    parameters?: any;
    responseSchema?: any;
    isEnabled: boolean;
  }>;
}

/**
 * Parse an API specification from text content
 */
export async function parseApiSpecification(
  content: string,
  contentType: string
): Promise<ParsedApiSpec> {
  let spec: any;

  try {
    // Parse based on content type
    if (contentType.includes("yaml") || contentType.includes("yml")) {
      spec = yaml.load(content);
    } else {
      spec = JSON.parse(content);
    }
  } catch (error) {
    throw new Error("Invalid JSON or YAML format");
  }

  if (!spec || typeof spec !== "object") {
    throw new Error("Invalid specification format");
  }

  // Validate OpenAPI/Swagger version
  const isOpenAPI3 = spec.openapi && spec.openapi.startsWith("3.");
  const isSwagger2 = spec.swagger && spec.swagger.startsWith("2.");

  if (!isOpenAPI3 && !isSwagger2) {
    throw new Error("Unsupported specification version. Please use OpenAPI 3.x or Swagger 2.x");
  }

  // Extract basic information
  const info = spec.info || {};
  const version = info.version;
  
  // Extract base URL
  let baseUrl: string | undefined;
  if (isOpenAPI3 && spec.servers && spec.servers.length > 0) {
    baseUrl = spec.servers[0].url;
  } else if (isSwagger2) {
    const host = spec.host;
    const basePath = spec.basePath || "";
    const schemes = spec.schemes || ["https"];
    if (host) {
      baseUrl = `${schemes[0]}://${host}${basePath}`;
    }
  }

  // Extract authentication information
  const { authType, authConfig } = extractAuthInfo(spec, isOpenAPI3);

  // Extract function tools from paths
  const functionTools = extractFunctionTools(spec, isOpenAPI3);

  return {
    schema: spec,
    baseUrl,
    version,
    authType,
    authConfig,
    functionTools,
  };
}

/**
 * Extract authentication information from the specification
 */
function extractAuthInfo(spec: any, isOpenAPI3: boolean): {
  authType?: ApiAuthType;
  authConfig?: any;
} {
  let authType: ApiAuthType | undefined;
  let authConfig: any = {};

  if (isOpenAPI3) {
    // OpenAPI 3.x security schemes
    const securitySchemes = spec.components?.securitySchemes || {};
    const firstScheme = Object.values(securitySchemes)[0] as any;

    if (firstScheme) {
      switch (firstScheme.type) {
        case "apiKey":
          authType = ApiAuthType.API_KEY;
          authConfig = {
            type: "apiKey",
            name: firstScheme.name,
            in: firstScheme.in,
            description: firstScheme.description,
          };
          break;
        case "http":
          if (firstScheme.scheme === "bearer") {
            authType = ApiAuthType.BEARER_TOKEN;
            authConfig = {
              type: "bearer",
              scheme: firstScheme.scheme,
              bearerFormat: firstScheme.bearerFormat,
              description: firstScheme.description,
            };
          } else if (firstScheme.scheme === "basic") {
            authType = ApiAuthType.BASIC_AUTH;
            authConfig = {
              type: "basic",
              description: firstScheme.description,
            };
          }
          break;
        case "oauth2":
          authType = ApiAuthType.OAUTH2;
          authConfig = {
            type: "oauth2",
            flows: firstScheme.flows,
            description: firstScheme.description,
          };
          break;
        default:
          authType = ApiAuthType.CUSTOM;
          authConfig = firstScheme;
      }
    }
  } else {
    // Swagger 2.x security definitions
    const securityDefinitions = spec.securityDefinitions || {};
    const firstDef = Object.values(securityDefinitions)[0] as any;

    if (firstDef) {
      switch (firstDef.type) {
        case "apiKey":
          authType = ApiAuthType.API_KEY;
          authConfig = {
            type: "apiKey",
            name: firstDef.name,
            in: firstDef.in,
            description: firstDef.description,
          };
          break;
        case "basic":
          authType = ApiAuthType.BASIC_AUTH;
          authConfig = {
            type: "basic",
            description: firstDef.description,
          };
          break;
        case "oauth2":
          authType = ApiAuthType.OAUTH2;
          authConfig = {
            type: "oauth2",
            flow: firstDef.flow,
            authorizationUrl: firstDef.authorizationUrl,
            tokenUrl: firstDef.tokenUrl,
            scopes: firstDef.scopes,
            description: firstDef.description,
          };
          break;
        default:
          authType = ApiAuthType.CUSTOM;
          authConfig = firstDef;
      }
    }
  }

  return { authType, authConfig };
}

/**
 * Extract function tools from API paths
 */
function extractFunctionTools(spec: any, isOpenAPI3: boolean): Array<{
  name: string;
  description?: string;
  endpoint: string;
  method: string;
  parameters?: any;
  responseSchema?: any;
  isEnabled: boolean;
}> {
  const functionTools: any[] = [];
  const paths = spec.paths || {};

  for (const [path, pathItem] of Object.entries(paths)) {
    if (!pathItem || typeof pathItem !== "object") continue;

    for (const [method, operation] of Object.entries(pathItem as any)) {
      if (!operation || typeof operation !== "object") continue;
      if (["parameters", "summary", "description"].includes(method)) continue;

      const httpMethod = method.toUpperCase();
      const operationId = operation.operationId || `${httpMethod.toLowerCase()}${path.replace(/[^a-zA-Z0-9]/g, "")}`;
      const summary = operation.summary || `${httpMethod} ${path}`;
      const description = operation.description;

      // Extract parameters
      const parameters = extractParameters(operation, pathItem, isOpenAPI3);

      // Extract response schema
      const responseSchema = extractResponseSchema(operation, isOpenAPI3);

      functionTools.push({
        name: operationId,
        description: description || summary,
        endpoint: path,
        method: httpMethod,
        parameters,
        responseSchema,
        isEnabled: true,
      });
    }
  }

  return functionTools;
}

/**
 * Extract parameters from an operation
 */
function extractParameters(operation: any, pathItem: any, isOpenAPI3: boolean): any {
  const parameters: any = {
    type: "object",
    properties: {},
    required: [],
  };

  // Combine path-level and operation-level parameters
  const allParams = [
    ...(pathItem.parameters || []),
    ...(operation.parameters || []),
  ];

  for (const param of allParams) {
    if (!param || typeof param !== "object") continue;

    const name = param.name;
    const required = param.required === true;
    const description = param.description;

    if (required) {
      parameters.required.push(name);
    }

    if (isOpenAPI3) {
      // OpenAPI 3.x parameter schema
      parameters.properties[name] = {
        ...param.schema,
        description,
      };
    } else {
      // Swagger 2.x parameter
      parameters.properties[name] = {
        type: param.type || "string",
        format: param.format,
        description,
        enum: param.enum,
        default: param.default,
      };
    }
  }

  // Handle request body for OpenAPI 3.x
  if (isOpenAPI3 && operation.requestBody) {
    const requestBody = operation.requestBody;
    const content = requestBody.content;
    
    if (content) {
      const jsonContent = content["application/json"];
      if (jsonContent && jsonContent.schema) {
        // Merge request body schema into parameters
        const bodySchema = jsonContent.schema;
        if (bodySchema.properties) {
          Object.assign(parameters.properties, bodySchema.properties);
          if (bodySchema.required) {
            parameters.required.push(...bodySchema.required);
          }
        }
      }
    }
  }

  return parameters;
}

/**
 * Extract response schema from an operation
 */
function extractResponseSchema(operation: any, isOpenAPI3: boolean): any {
  const responses = operation.responses || {};
  
  // Look for successful response (200, 201, etc.)
  const successResponse = responses["200"] || responses["201"] || responses["default"];
  
  if (!successResponse) {
    return undefined;
  }

  if (isOpenAPI3) {
    // OpenAPI 3.x response schema
    const content = successResponse.content;
    if (content) {
      const jsonContent = content["application/json"];
      return jsonContent?.schema;
    }
  } else {
    // Swagger 2.x response schema
    return successResponse.schema;
  }

  return undefined;
}
