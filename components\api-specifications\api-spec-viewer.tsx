import dynamic from "next/dynamic";
import { useState, useEffect } from "react";

import LoadingSpinner from "@/components/ui/loading-spinner";

// Dynamically import Stoplight Elements to avoid SSR issues
const StoplightElements = dynamic(
  () => import("./stoplight-elements-wrapper"),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center h-64 border rounded-lg">
        <LoadingSpinner className="h-8 w-8" />
      </div>
    ),
  }
);

interface ApiSpecViewerProps {
  specification: any;
  baseUrl?: string;
}

export function ApiSpecViewer({ specification, baseUrl }: ApiSpecViewerProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!specification) {
    return (
      <div className="flex items-center justify-center h-64 border rounded-lg">
        <div className="text-center">
          <p className="text-muted-foreground">No API specification available</p>
        </div>
      </div>
    );
  }

  if (!mounted) {
    return (
      <div className="flex items-center justify-center h-64 border rounded-lg">
        <LoadingSpinner className="h-8 w-8" />
      </div>
    );
  }

  return (
    <div className="w-full">
      <StoplightElements
        specification={specification}
        baseUrl={baseUrl}
      />
    </div>
  );
}
