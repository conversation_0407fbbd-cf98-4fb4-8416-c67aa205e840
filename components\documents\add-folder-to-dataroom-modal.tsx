import { useRouter } from "next/router";

import { useState } from "react";

import { useTeam } from "@/context/team-context";
import { toast } from "sonner";
import { mutate } from "swr";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import useDatarooms from "@/lib/swr/use-datarooms";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

export type TSelectedDataroom = { id: string; name: string } | null;

export function AddFolderToDataroomModal({
  open,
  setOpen,
  folderId,
  folderName,
  dataroomId,
}: {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  folderId?: string;
  folderName?: string;
  dataroomId?: string;
}) {
  const router = useRouter();
  const [selectedDataroom, setSelectedDataroom] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const teamInfo = useTeam();
  const teamId = teamInfo?.currentTeam?.id;

  const { datarooms } = useDatarooms();

  const handleSubmit = async (event: any) => {
    event.preventDefault();
    event.stopPropagation();

    if (!selectedDataroom) return;

    setLoading(true);
    try {
      const response = await fetch(
        !!dataroomId
          ? `/api/teams/${teamId}/datarooms/${dataroomId}/folders/manage/${folderId}/dataroom-to-dataroom`
          : `/api/teams/${teamId}/folders/manage/${folderId}/add-to-dataroom`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            dataroomId: selectedDataroom,
          }),
        },
      );

      if (!response.ok) {
        const { message } = await response.json();
        setLoading(false);
        toast.error(message);
        return;
      }
      dataroomId &&
        mutate(`/api/teams/${teamId}/datarooms/${dataroomId}/folders`);

      const dataroomName = datarooms?.find(
        (d) => d.id === selectedDataroom,
      )?.name;

      toast.success(`Folder added successfully!`, {
        description: `${folderName?.trim()} → ${dataroomName}`,
        action: {
          label: "Open Dataroom",
          onClick: () =>
            router.push(`/datarooms/${selectedDataroom}/documents`),
        },
        duration: 10000,
      });
    } catch (error) {
      console.error("Error adding folder to dataroom", error);
      toast.error("Failed to add folder to dataroom. Try again.");
    } finally {
      setLoading(false);
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader className="text-start">
          <DialogTitle>
            <span className="font-bold">{folderName}</span>
          </DialogTitle>
          <DialogDescription>Add your folder to a dataroom.</DialogDescription>
        </DialogHeader>
        <Select onValueChange={(value) => setSelectedDataroom(value)}>
          <SelectTrigger className="min-w-fit">
            <SelectValue placeholder="Select a dataroom" />
          </SelectTrigger>
          <SelectContent>
            {datarooms?.map((dataroom) => (
              <SelectItem
                key={dataroom.id}
                value={dataroom.id}
                disabled={dataroom.id === dataroomId}
              >
                {dataroom.name}
                {dataroom.id === dataroomId ? " (current)" : ""}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <form>
          <div className="mb-2"></div>

          <DialogFooter>
            <Button
              onClick={handleSubmit}
              className="flex h-9 w-full gap-1"
              loading={loading}
              disabled={!selectedDataroom}
            >
              {!selectedDataroom ? (
                "Select a dataroom"
              ) : (
                <>
                  Add to{" "}
                  <span className="font-medium">
                    {
                      datarooms?.filter((d) => d.id === selectedDataroom)[0]
                        .name
                    }
                  </span>
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
