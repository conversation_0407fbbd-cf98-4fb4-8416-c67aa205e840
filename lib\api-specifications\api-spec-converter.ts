/**
 * Utility to convert API specifications to OpenAI function tool format
 * Handles parameter mapping, schema resolution, and proper formatting for the OpenAI Agents SDK
 */

export interface OpenAIFunctionTool {
  type: "function";
  function: {
    name: string;
    description: string;
    parameters: {
      type: "object";
      properties: Record<string, any>;
      required: string[];
    };
  };
}

export interface FunctionToolData {
  id: string;
  name: string;
  description?: string;
  endpoint: string;
  method: string;
  parameters?: any;
  responseSchema?: any;
  isEnabled: boolean;
}

/**
 * Convert a single function tool to OpenAI format
 */
export function convertFunctionToolToOpenAI(tool: FunctionToolData): OpenAIFunctionTool {
  return {
    type: "function",
    function: {
      name: tool.name,
      description: tool.description || `${tool.method.toUpperCase()} ${tool.endpoint}`,
      parameters: tool.parameters || {
        type: "object",
        properties: {},
        required: [],
      },
    },
  };
}

/**
 * Convert multiple function tools to OpenAI format
 */
export function convertFunctionToolsToOpenAI(tools: FunctionToolData[]): OpenAIFunctionTool[] {
  return tools
    .filter(tool => tool.isEnabled)
    .map(tool => convertFunctionToolToOpenAI(tool));
}

/**
 * Convert API specification paths to OpenAI function tools
 * This is a more comprehensive converter that processes raw API spec data
 */
export function convertApiSpecToOpenAITools(
  apiSpec: any,
  baseUrl?: string
): OpenAIFunctionTool[] {
  const tools: OpenAIFunctionTool[] = [];
  const paths = apiSpec.paths || {};

  for (const [path, pathItem] of Object.entries(paths)) {
    if (!pathItem || typeof pathItem !== "object") continue;

    const httpMethods = ["get", "post", "put", "patch", "delete", "head", "options"];
    
    for (const method of httpMethods) {
      const operation = (pathItem as any)[method];
      if (!operation) continue;

      const operationId = operation.operationId || generateOperationId(method, path);
      const summary = operation.summary || "";
      const description = operation.description || summary || `${method.toUpperCase()} ${path}`;

      // Extract parameters
      const parameters = extractParametersFromOperation(operation, pathItem, apiSpec);

      tools.push({
        type: "function",
        function: {
          name: operationId,
          description: description,
          parameters: parameters,
        },
      });
    }
  }

  return tools;
}

/**
 * Generate operation ID from method and path
 */
function generateOperationId(method: string, path: string): string {
  // Convert path like "/users/{id}" to "getUsersById"
  const cleanPath = path
    .replace(/[{}]/g, "") // Remove braces
    .replace(/[^a-zA-Z0-9]/g, "_") // Replace non-alphanumeric with underscore
    .replace(/_+/g, "_") // Replace multiple underscores with single
    .replace(/^_|_$/g, ""); // Remove leading/trailing underscores

  const methodPrefix = method.toLowerCase();
  const pathParts = cleanPath.split("_").filter(Boolean);
  
  // Capitalize each part except the first
  const camelCasePath = pathParts
    .map((part, index) => index === 0 ? part : part.charAt(0).toUpperCase() + part.slice(1))
    .join("");

  return `${methodPrefix}${camelCasePath.charAt(0).toUpperCase() + camelCasePath.slice(1)}`;
}

/**
 * Extract parameters from operation and path item
 */
function extractParametersFromOperation(
  operation: any,
  pathItem: any,
  apiSpec: any
): {
  type: "object";
  properties: Record<string, any>;
  required: string[];
} {
  const parameters = {
    type: "object" as const,
    properties: {} as Record<string, any>,
    required: [] as string[],
  };

  // Combine path-level and operation-level parameters
  const allParams = [
    ...(pathItem.parameters || []),
    ...(operation.parameters || []),
  ];

  // Process parameters
  for (const param of allParams) {
    const resolvedParam = resolveReference(param, apiSpec);
    if (!resolvedParam || !resolvedParam.name) continue;

    const paramSchema = convertParameterToJsonSchema(resolvedParam);
    parameters.properties[resolvedParam.name] = paramSchema;

    if (resolvedParam.required) {
      parameters.required.push(resolvedParam.name);
    }
  }

  // Handle request body (OpenAPI 3.0)
  if (operation.requestBody) {
    const requestBody = resolveReference(operation.requestBody, apiSpec);
    if (requestBody && requestBody.content) {
      const jsonContent = requestBody.content["application/json"];
      if (jsonContent && jsonContent.schema) {
        const bodySchema = resolveReference(jsonContent.schema, apiSpec);
        
        // If the body schema is an object, merge its properties
        if (bodySchema.type === "object" && bodySchema.properties) {
          Object.assign(parameters.properties, bodySchema.properties);
          if (bodySchema.required) {
            parameters.required.push(...bodySchema.required);
          }
        } else {
          // Otherwise, add the entire body as a single parameter
          parameters.properties.body = bodySchema;
          if (requestBody.required) {
            parameters.required.push("body");
          }
        }
      }
    }
  }

  // Handle body parameter (Swagger 2.0)
  const bodyParam = allParams.find(p => p.in === "body");
  if (bodyParam) {
    const resolvedBodyParam = resolveReference(bodyParam, apiSpec);
    if (resolvedBodyParam.schema) {
      const bodySchema = resolveReference(resolvedBodyParam.schema, apiSpec);
      
      // If the body schema is an object, merge its properties
      if (bodySchema.type === "object" && bodySchema.properties) {
        Object.assign(parameters.properties, bodySchema.properties);
        if (bodySchema.required) {
          parameters.required.push(...bodySchema.required);
        }
      } else {
        // Otherwise, add the entire body as a single parameter
        parameters.properties[resolvedBodyParam.name || "body"] = bodySchema;
        if (resolvedBodyParam.required) {
          parameters.required.push(resolvedBodyParam.name || "body");
        }
      }
    }
  }

  return parameters;
}

/**
 * Convert parameter to JSON Schema
 */
function convertParameterToJsonSchema(param: any): any {
  const schema: any = {
    type: param.type || "string",
    description: param.description,
  };

  if (param.enum) {
    schema.enum = param.enum;
  }

  if (param.format) {
    schema.format = param.format;
  }

  if (param.items) {
    schema.items = convertParameterToJsonSchema(param.items);
  }

  if (param.minimum !== undefined) {
    schema.minimum = param.minimum;
  }

  if (param.maximum !== undefined) {
    schema.maximum = param.maximum;
  }

  if (param.pattern) {
    schema.pattern = param.pattern;
  }

  return schema;
}

/**
 * Resolve $ref references in the API specification
 */
function resolveReference(obj: any, apiSpec: any): any {
  if (!obj || typeof obj !== "object") {
    return obj;
  }

  if (obj.$ref) {
    const refPath = obj.$ref.replace("#/", "").split("/");
    let resolved = apiSpec;
    
    for (const part of refPath) {
      resolved = resolved[part];
      if (!resolved) {
        console.warn(`Could not resolve reference: ${obj.$ref}`);
        return obj;
      }
    }
    
    return resolveReference(resolved, apiSpec);
  }

  // Recursively resolve references in nested objects
  const result = Array.isArray(obj) ? [] : {};
  for (const [key, value] of Object.entries(obj)) {
    (result as any)[key] = resolveReference(value, apiSpec);
  }

  return result;
}
