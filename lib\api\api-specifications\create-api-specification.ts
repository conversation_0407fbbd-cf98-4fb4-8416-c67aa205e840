import { ApiAuthType } from "@prisma/client";
import prisma from "@/lib/prisma";

export type ApiSpecificationData = {
  name: string;
  description?: string;
  baseUrl?: string;
  apiVersion?: string;
  authType?: ApiAuthType;
  authConfig?: any;
  parsedSchema?: any;
  functionTools?: FunctionToolData[];
};

export type FunctionToolData = {
  name: string;
  description?: string;
  endpoint: string;
  method: string;
  parameters?: any;
  responseSchema?: any;
  isEnabled?: boolean;
};

export type CreateApiSpecificationParams = {
  documentId: string;
  apiSpecData: ApiSpecificationData;
  teamId: string;
  ownerId?: string;
};

/**
 * Creates an API specification linked to a document
 */
export const createApiSpecification = async ({
  documentId,
  apiSpecData,
  teamId,
  ownerId,
}: CreateApiSpecificationParams) => {
  const {
    name,
    description,
    baseUrl,
    apiVersion,
    authType,
    authConfig,
    parsedSchema,
    functionTools = [],
  } = apiSpecData;

  // Verify the document exists and belongs to the team
  const document = await prisma.document.findUnique({
    where: {
      id: documentId,
      teamId: teamId,
    },
  });

  if (!document) {
    throw new Error("Document not found or does not belong to the team");
  }

  // Check if an API specification already exists for this document
  const existingApiSpec = await prisma.apiSpecification.findUnique({
    where: {
      documentId: documentId,
    },
  });

  if (existingApiSpec) {
    throw new Error("API specification already exists for this document");
  }

  // Create the API specification with function tools
  const apiSpecification = await prisma.apiSpecification.create({
    data: {
      name,
      description,
      baseUrl,
      apiVersion,
      authType,
      authConfig,
      parsedSchema,
      documentId,
      teamId,
      ownerId,
      functionTools: {
        create: functionTools.map((tool) => ({
          name: tool.name,
          description: tool.description,
          endpoint: tool.endpoint,
          method: tool.method,
          parameters: tool.parameters,
          responseSchema: tool.responseSchema,
          isEnabled: tool.isEnabled ?? true,
        })),
      },
    },
    include: {
      document: {
        select: {
          id: true,
          name: true,
          type: true,
          contentType: true,
        },
      },
      functionTools: true,
      team: {
        select: {
          id: true,
          name: true,
        },
      },
      owner: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  return apiSpecification;
};

/**
 * Updates an existing API specification
 */
export const updateApiSpecification = async (
  apiSpecId: string,
  updateData: Partial<ApiSpecificationData>,
  teamId: string
) => {
  // Verify the API specification exists and belongs to the team
  const existingApiSpec = await prisma.apiSpecification.findUnique({
    where: {
      id: apiSpecId,
      teamId: teamId,
    },
  });

  if (!existingApiSpec) {
    throw new Error("API specification not found or does not belong to the team");
  }

  const {
    name,
    description,
    baseUrl,
    apiVersion,
    authType,
    authConfig,
    parsedSchema,
    functionTools,
  } = updateData;

  // Update the API specification
  const updatedApiSpec = await prisma.apiSpecification.update({
    where: {
      id: apiSpecId,
    },
    data: {
      ...(name && { name }),
      ...(description !== undefined && { description }),
      ...(baseUrl !== undefined && { baseUrl }),
      ...(apiVersion !== undefined && { apiVersion }),
      ...(authType !== undefined && { authType }),
      ...(authConfig !== undefined && { authConfig }),
      ...(parsedSchema !== undefined && { parsedSchema }),
    },
    include: {
      document: {
        select: {
          id: true,
          name: true,
          type: true,
          contentType: true,
        },
      },
      functionTools: true,
      team: {
        select: {
          id: true,
          name: true,
        },
      },
      owner: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  // Update function tools if provided
  if (functionTools && functionTools.length > 0) {
    // Delete existing function tools
    await prisma.functionTool.deleteMany({
      where: {
        apiSpecificationId: apiSpecId,
      },
    });

    // Create new function tools
    await prisma.functionTool.createMany({
      data: functionTools.map((tool) => ({
        name: tool.name,
        description: tool.description,
        endpoint: tool.endpoint,
        method: tool.method,
        parameters: tool.parameters,
        responseSchema: tool.responseSchema,
        isEnabled: tool.isEnabled ?? true,
        apiSpecificationId: apiSpecId,
      })),
    });
  }

  return updatedApiSpec;
};

/**
 * Gets an API specification by document ID
 */
export const getApiSpecificationByDocumentId = async (
  documentId: string,
  teamId: string
) => {
  const apiSpecification = await prisma.apiSpecification.findUnique({
    where: {
      documentId: documentId,
      teamId: teamId,
    },
    include: {
      document: {
        select: {
          id: true,
          name: true,
          type: true,
          contentType: true,
          file: true,
          originalFile: true,
        },
      },
      functionTools: {
        orderBy: {
          name: "asc",
        },
      },
      team: {
        select: {
          id: true,
          name: true,
        },
      },
      owner: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  return apiSpecification;
};

/**
 * Lists all API specifications for a team
 */
export const listApiSpecifications = async (teamId: string) => {
  const apiSpecifications = await prisma.apiSpecification.findMany({
    where: {
      teamId: teamId,
    },
    include: {
      document: {
        select: {
          id: true,
          name: true,
          type: true,
          contentType: true,
        },
      },
      _count: {
        select: {
          functionTools: true,
        },
      },
      owner: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  return apiSpecifications;
};

/**
 * Deletes an API specification
 */
export const deleteApiSpecification = async (
  apiSpecId: string,
  teamId: string
) => {
  // Verify the API specification exists and belongs to the team
  const existingApiSpec = await prisma.apiSpecification.findUnique({
    where: {
      id: apiSpecId,
      teamId: teamId,
    },
  });

  if (!existingApiSpec) {
    throw new Error("API specification not found or does not belong to the team");
  }

  // Delete the API specification (function tools will be deleted automatically due to cascade)
  await prisma.apiSpecification.delete({
    where: {
      id: apiSpecId,
    },
  });

  return { success: true };
};
