-- CreateEnum
CREATE TYPE "ApiAuthType" AS ENUM ('NONE', 'API_KEY', 'BEARER_TOKEN', 'BASIC_AUTH', 'OAUTH2', 'CUSTOM');

-- AlterTable
ALTER TABLE "Document" ADD COLUMN     "apiVersion" TEXT,
ADD COLUMN     "authConfig" JSONB,
ADD COLUMN     "authType" "ApiAuthType",
ADD COLUMN     "baseUrl" TEXT,
ADD COLUMN     "endpoints" JSONB,
ADD COLUMN     "isApiSpec" BOOLEAN NOT NULL DEFAULT false;

-- CreateIndex
CREATE INDEX "Document_isApiSpec_idx" ON "Document"("isApiSpec");
