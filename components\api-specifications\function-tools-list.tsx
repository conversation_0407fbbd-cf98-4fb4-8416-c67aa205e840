import { useState } from "react";

import { 
  SettingsIcon, 
  ToggleLeftIcon, 
  ToggleRightIcon,
  CodeIcon,
  EditIcon,
  EyeIcon,
} from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface FunctionTool {
  id: string;
  name: string;
  description?: string;
  endpoint: string;
  method: string;
  parameters?: any;
  responseSchema?: any;
  isEnabled: boolean;
  createdAt: string;
  updatedAt: string;
}

interface FunctionToolsListProps {
  functionTools: FunctionTool[];
  apiSpecificationId: string;
  teamId: string;
  onUpdate: () => void;
}

export function FunctionToolsList({
  functionTools,
  apiSpecificationId,
  teamId,
  onUpdate,
}: FunctionToolsListProps) {
  const [updatingTools, setUpdatingTools] = useState<Set<string>>(new Set());

  const handleToggleEnabled = async (toolId: string, enabled: boolean) => {
    setUpdatingTools(prev => new Set(prev).add(toolId));
    
    try {
      const response = await fetch(
        `/api/teams/${teamId}/api-specifications/${apiSpecificationId}/function-tools/${toolId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            isEnabled: enabled,
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update function tool");
      }

      toast.success(`Function tool ${enabled ? "enabled" : "disabled"}`);
      onUpdate();
    } catch (error) {
      console.error("Error updating function tool:", error);
      toast.error("Failed to update function tool");
    } finally {
      setUpdatingTools(prev => {
        const newSet = new Set(prev);
        newSet.delete(toolId);
        return newSet;
      });
    }
  };

  const getMethodColor = (method: string) => {
    switch (method.toUpperCase()) {
      case "GET":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "POST":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "PUT":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case "PATCH":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "DELETE":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  if (!functionTools || functionTools.length === 0) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center space-y-4">
          <div className="mx-auto h-12 w-12 rounded-full bg-muted flex items-center justify-center">
            <SettingsIcon className="h-6 w-6 text-muted-foreground" />
          </div>
          <div>
            <h3 className="text-lg font-semibold">No function tools</h3>
            <p className="text-muted-foreground">
              No function tools were generated from this API specification
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Function Tools</h3>
          <p className="text-sm text-muted-foreground">
            {functionTools.length} tools generated from API endpoints
          </p>
        </div>
        <div className="text-sm text-muted-foreground">
          {functionTools.filter(tool => tool.isEnabled).length} enabled
        </div>
      </div>

      <div className="grid gap-4">
        {functionTools.map((tool) => (
          <Card key={tool.id} className={`transition-opacity ${!tool.isEnabled ? "opacity-60" : ""}`}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <CardTitle className="flex items-center gap-2">
                    <CodeIcon className="h-4 w-4" />
                    {tool.name}
                  </CardTitle>
                  {tool.description && (
                    <CardDescription>{tool.description}</CardDescription>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={tool.isEnabled}
                    onCheckedChange={(enabled) => handleToggleEnabled(tool.id, enabled)}
                    disabled={updatingTools.has(tool.id)}
                  />
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <div className="space-y-3">
                {/* Endpoint */}
                <div className="flex items-center gap-2">
                  <Badge className={getMethodColor(tool.method)}>
                    {tool.method.toUpperCase()}
                  </Badge>
                  <code className="text-sm bg-muted px-2 py-1 rounded">
                    {tool.endpoint}
                  </code>
                </div>

                {/* Parameters and Response Schema */}
                <div className="flex gap-4">
                  {tool.parameters && (
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <EyeIcon className="h-4 w-4 mr-2" />
                          View Parameters
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Parameters - {tool.name}</DialogTitle>
                          <DialogDescription>
                            Function parameters schema
                          </DialogDescription>
                        </DialogHeader>
                        <pre className="bg-muted p-4 rounded text-xs overflow-auto">
                          {JSON.stringify(tool.parameters, null, 2)}
                        </pre>
                      </DialogContent>
                    </Dialog>
                  )}

                  {tool.responseSchema && (
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <EyeIcon className="h-4 w-4 mr-2" />
                          View Response
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Response Schema - {tool.name}</DialogTitle>
                          <DialogDescription>
                            Expected response schema
                          </DialogDescription>
                        </DialogHeader>
                        <pre className="bg-muted p-4 rounded text-xs overflow-auto">
                          {JSON.stringify(tool.responseSchema, null, 2)}
                        </pre>
                      </DialogContent>
                    </Dialog>
                  )}
                </div>

                {/* Status */}
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>Status:</span>
                  <Badge variant={tool.isEnabled ? "default" : "secondary"}>
                    {tool.isEnabled ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
