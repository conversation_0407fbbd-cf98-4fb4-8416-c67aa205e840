import { NextApiRequest, NextApiResponse } from "next";
import { getServerSession } from "next-auth/next";
import { ApiAuthType } from "@prisma/client";

import { authOptions } from "@/pages/api/auth/[...nextauth]";
import { 
  updateApiSpecification,
  deleteApiSpecification,
  ApiSpecificationData,
} from "@/lib/api/api-specifications/create-api-specification";
import { errorhandler } from "@/lib/errorHandler";
import { CustomUser } from "@/lib/types";
import { getTeamWithUsersAndDocument } from "@/lib/team/helper";
import prisma from "@/lib/prisma";

export default async function handle(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).end("Unauthorized");
  }

  const { teamId, id: apiSpecId } = req.query as { 
    teamId: string; 
    id: string; 
  };
  const userId = (session.user as CustomUser).id;

  try {
    // Verify user is part of the team
    await getTeamWithUsersAndDocument({
      teamId,
      userId,
    });
  } catch (error) {
    return errorhandler(error, res);
  }

  if (req.method === "GET") {
    // GET /api/teams/:teamId/api-specifications/:id
    try {
      const apiSpecification = await prisma.apiSpecification.findUnique({
        where: {
          id: apiSpecId,
          teamId: teamId,
        },
        include: {
          document: {
            select: {
              id: true,
              name: true,
              type: true,
              contentType: true,
              file: true,
              originalFile: true,
            },
          },
          functionTools: {
            orderBy: {
              name: "asc",
            },
          },
          team: {
            select: {
              id: true,
              name: true,
            },
          },
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!apiSpecification) {
        return res.status(404).json({ error: "API specification not found" });
      }

      return res.status(200).json(apiSpecification);
    } catch (error) {
      errorhandler(error, res);
    }
  } else if (req.method === "PUT") {
    // PUT /api/teams/:teamId/api-specifications/:id
    const {
      name,
      description,
      baseUrl,
      apiVersion,
      authType,
      authConfig,
      parsedSchema,
      functionTools,
    } = req.body as Partial<{
      name: string;
      description: string;
      baseUrl: string;
      apiVersion: string;
      authType: ApiAuthType;
      authConfig: any;
      parsedSchema: any;
      functionTools: Array<{
        name: string;
        description?: string;
        endpoint: string;
        method: string;
        parameters?: any;
        responseSchema?: any;
        isEnabled?: boolean;
      }>;
    }>;

    try {
      const updateData: Partial<ApiSpecificationData> = {
        name,
        description,
        baseUrl,
        apiVersion,
        authType,
        authConfig,
        parsedSchema,
        functionTools,
      };

      const updatedApiSpec = await updateApiSpecification(
        apiSpecId,
        updateData,
        teamId
      );

      return res.status(200).json(updatedApiSpec);
    } catch (error) {
      errorhandler(error, res);
    }
  } else if (req.method === "DELETE") {
    // DELETE /api/teams/:teamId/api-specifications/:id
    try {
      await deleteApiSpecification(apiSpecId, teamId);
      return res.status(200).json({ success: true });
    } catch (error) {
      errorhandler(error, res);
    }
  } else {
    // We only allow GET, PUT, and DELETE requests
    res.setHeader("Allow", ["GET", "PUT", "DELETE"]);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
