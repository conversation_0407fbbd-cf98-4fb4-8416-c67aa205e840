import { useRouter } from "next/router";
import { useState } from "react";

import { useTeam } from "@/context/team-context";
import { PlusIcon, SearchIcon } from "lucide-react";
import useSWR from "swr";

import { AddApiSpecModal } from "@/components/api-specifications/add-api-spec-modal";
import { ApiSpecificationsList } from "@/components/api-specifications/api-specifications-list";
import AppLayout from "@/components/layouts/app";
import { SearchBoxPersisted } from "@/components/search-box";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

import { fetcher } from "@/lib/utils";

export default function ApiSpecificationsPage() {
  const router = useRouter();
  const teamInfo = useTeam();
  const [searchQuery, setSearchQuery] = useState("");

  const teamId = teamInfo?.currentTeam?.id;

  const {
    data: apiSpecifications,
    error,
    mutate,
    isValidating,
  } = useSWR(
    teamId ? `/api/teams/${teamId}/api-specifications` : null,
    fetcher,
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000,
    }
  );

  const filteredApiSpecs = apiSpecifications?.filter((spec: any) =>
    spec.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    spec.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (!teamInfo?.currentTeam) {
    return (
      <AppLayout>
        <div className="flex h-screen items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-semibold">No team selected</h2>
            <p className="text-muted-foreground">Please select a team to continue.</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <main className="relative mx-2 mb-10 mt-4 space-y-8 overflow-hidden px-1 sm:mx-3 md:mx-5 md:mt-5 lg:mx-7 lg:mt-8 xl:mx-10">
        {/* Header */}
        <section className="flex items-center justify-between">
          <div className="space-y-1">
            <h2 className="text-2xl font-bold tracking-tight text-foreground">
              API Specifications
            </h2>
            <p className="text-sm text-muted-foreground">
              Manage your API specifications and generate function tools
            </p>
          </div>

          <div className="flex items-center gap-x-2">
            <AddApiSpecModal>
              <Button className="flex-1 text-left">
                <PlusIcon className="h-5 w-5 shrink-0" aria-hidden="true" />
                <span className="text-xs sm:text-base">Add API Specification</span>
              </Button>
            </AddApiSpecModal>
          </div>
        </section>

        <Separator className="bg-gray-200 dark:bg-gray-800" />

        {/* Search and Filters */}
        <div className="mb-2 flex justify-end gap-x-2">
          <div className="relative w-full sm:max-w-xs">
            <SearchBoxPersisted 
              loading={isValidating} 
              inputClassName="h-10"
              onSearch={setSearchQuery}
            />
          </div>
        </div>

        {/* API Specifications List */}
        <ApiSpecificationsList
          apiSpecifications={filteredApiSpecs}
          teamInfo={teamInfo}
          loading={!apiSpecifications && !error}
          error={error}
          mutate={mutate}
        />
      </main>
    </AppLayout>
  );
}
