/*
  Warnings:

  - You are about to drop the column `apiVersion` on the `Document` table. All the data in the column will be lost.
  - You are about to drop the column `authConfig` on the `Document` table. All the data in the column will be lost.
  - You are about to drop the column `authType` on the `Document` table. All the data in the column will be lost.
  - You are about to drop the column `baseUrl` on the `Document` table. All the data in the column will be lost.
  - You are about to drop the column `endpoints` on the `Document` table. All the data in the column will be lost.
  - You are about to drop the column `isApiSpec` on the `Document` table. All the data in the column will be lost.

*/
-- DropIndex
DROP INDEX "Document_isApiSpec_idx";

-- AlterTable
ALTER TABLE "Document" DROP COLUMN "apiVersion",
DROP COLUMN "authConfig",
DROP COLUMN "authType",
DROP COLUMN "baseUrl",
DROP COLUMN "endpoints",
DROP COLUMN "isApiSpec";

-- CreateTable
CREATE TABLE "ApiSpecification" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "baseUrl" TEXT,
    "apiVersion" TEXT,
    "authType" "ApiAuthType",
    "authConfig" JSONB,
    "parsedSchema" JSONB,
    "documentId" TEXT NOT NULL,
    "teamId" TEXT NOT NULL,
    "ownerId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ApiSpecification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FunctionTool" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "apiSpecificationId" TEXT NOT NULL,
    "endpoint" TEXT NOT NULL,
    "method" TEXT NOT NULL,
    "parameters" JSONB,
    "responseSchema" JSONB,
    "isEnabled" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FunctionTool_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ApiSpecification_documentId_key" ON "ApiSpecification"("documentId");

-- CreateIndex
CREATE INDEX "ApiSpecification_teamId_idx" ON "ApiSpecification"("teamId");

-- CreateIndex
CREATE INDEX "ApiSpecification_ownerId_idx" ON "ApiSpecification"("ownerId");

-- CreateIndex
CREATE INDEX "FunctionTool_apiSpecificationId_idx" ON "FunctionTool"("apiSpecificationId");

-- CreateIndex
CREATE INDEX "FunctionTool_isEnabled_idx" ON "FunctionTool"("isEnabled");

-- AddForeignKey
ALTER TABLE "ApiSpecification" ADD CONSTRAINT "ApiSpecification_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiSpecification" ADD CONSTRAINT "ApiSpecification_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "Team"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiSpecification" ADD CONSTRAINT "ApiSpecification_ownerId_fkey" FOREIGN KEY ("ownerId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FunctionTool" ADD CONSTRAINT "FunctionTool_apiSpecificationId_fkey" FOREIGN KEY ("apiSpecificationId") REFERENCES "ApiSpecification"("id") ON DELETE CASCADE ON UPDATE CASCADE;
