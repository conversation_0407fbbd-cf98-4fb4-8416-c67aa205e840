import { useRouter } from "next/router";

import { use<PERSON><PERSON>back, useMemo, useRef, useState } from "react";

import { useTeam } from "@/context/team-context";
import { DocumentStorageType } from "@prisma/client";
import { useSession } from "next-auth/react";
import { DropEvent, FileRejection, useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { mutate } from "swr";

import { useAnalytics } from "@/lib/analytics";
import { SUPPORTED_DOCUMENT_MIME_TYPES } from "@/lib/constants";
import { DocumentData, createDocument } from "@/lib/documents/create-document";
import { resumableUpload } from "@/lib/files/tus-upload";
import {
  createFolderInBoth,
  createFolderInMainDocs,
  isSystemFile,
} from "@/lib/folders/create-folder";
import { usePlan } from "@/lib/swr/use-billing";
import useLimits from "@/lib/swr/use-limits";
import { CustomUser } from "@/lib/types";
import { cn } from "@/lib/utils";
import { getSupportedContentType } from "@/lib/utils/get-content-type";
import {
  getFileSizeLimit,
  getFileSizeLimits,
} from "@/lib/utils/get-file-size-limits";
import { getPagesCount } from "@/lib/utils/get-page-number-count";

// Originally these mime values were directly used in the dropzone hook.
// There was a solid reason to take them out of the scope, primarily to solve a browser compatibility issue to determine the file type when user dropped a folder.
// you will figure out how this change helped to fix the compatibility issue once you have went through reading of `getFilesFromDropEvent` and `traverseFolder`
const acceptableDropZoneMimeTypesWhenIsFreePlanAndNotTrail = {
  "application/pdf": [], // ".pdf"
  "application/vnd.ms-excel": [], // ".xls"
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [], // ".xlsx"
  "text/csv": [], // ".csv"
  "application/vnd.oasis.opendocument.spreadsheet": [], // ".ods"
  "image/png": [], // ".png"
  "image/jpeg": [], // ".jpeg"
  "image/jpg": [], // ".jpg"
};
const allAcceptableDropZoneMimeTypes = {
  "application/pdf": [], // ".pdf"
  "application/vnd.ms-excel": [], // ".xls"
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [], // ".xlsx"
  "application/vnd.ms-excel.sheet.macroEnabled.12": [".xlsm"], // ".xlsm"
  "text/csv": [], // ".csv"
  "application/vnd.oasis.opendocument.spreadsheet": [], // ".ods"
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [], // ".docx"
  "application/vnd.openxmlformats-officedocument.presentationml.presentation":
    [], // ".pptx"
  "application/vnd.ms-powerpoint": [], // ".ppt"
  "application/msword": [], // ".doc"
  "application/vnd.oasis.opendocument.text": [], // ".odt"
  "application/vnd.oasis.opendocument.presentation": [], // ".odp"
  "image/vnd.dwg": [".dwg"], // ".dwg"
  "image/vnd.dxf": [".dxf"], // ".dxf"
  "image/png": [], // ".png"
  "image/jpeg": [], // ".jpeg"
  "image/jpg": [], // ".jpg"
  "application/zip": [], // ".zip"
  "application/x-zip-compressed": [], // ".zip"
  "video/mp4": [], // ".mp4"
  "video/webm": [], // ".webm"
  "video/quicktime": [], // ".mov"
  "video/x-msvideo": [], // ".avi"
  "video/ogg": [], // ".ogg"
  "application/vnd.google-earth.kml+xml": [".kml"], // ".kml"
  "application/vnd.google-earth.kmz": [".kmz"], // ".kmz"
  "application/vnd.ms-outlook": [".msg"], // ".msg"
};

interface FileWithPaths extends File {
  path?: string;
  whereToUploadPath?: string;
}

export interface UploadState {
  fileName: string;
  progress: number;
  documentId?: string;
  uploadId: string;
}

export interface RejectedFile {
  fileName: string;
  message: string;
}

interface UploadZoneProps extends React.PropsWithChildren {
  onUploadStart: (uploads: UploadState[]) => void;
  onUploadProgress: (
    index: number,
    progress: number,
    documentId?: string,
  ) => void;
  onUploadRejected: (rejected: RejectedFile[]) => void;
  onUploadSuccess?: (
    files: {
      fileName: string;
      documentId: string;
      dataroomDocumentId: string;
    }[],
  ) => void;
  setUploads: React.Dispatch<React.SetStateAction<UploadState[]>>;
  setRejectedFiles: React.Dispatch<React.SetStateAction<RejectedFile[]>>;
  folderPathName?: string;
  dataroomId?: string;
}

export default function UploadZone({
  children,
  onUploadStart,
  onUploadProgress,
  onUploadRejected,
  onUploadSuccess,
  folderPathName,
  setUploads,
  setRejectedFiles,
  dataroomId,
}: UploadZoneProps) {
  const analytics = useAnalytics();
  const { plan, isFree, isTrial } = usePlan();
  const router = useRouter();
  const teamInfo = useTeam();
  const { data: session } = useSession();
  const { limits, canAddDocuments } = useLimits();
  const remainingDocuments = limits?.documents
    ? limits?.documents - limits?.usage?.documents
    : 0;

  const [progress, setProgress] = useState<number>(0);
  const [showProgress, setShowProgress] = useState(false);
  const uploadProgress = useRef<number[]>([]);

  const fileSizeLimits = useMemo(
    () =>
      getFileSizeLimits({
        limits,
        isFree,
        isTrial,
      }),
    [limits, isFree, isTrial],
  );

  const acceptableDropZoneFileTypes =
    isFree && !isTrial
      ? acceptableDropZoneMimeTypesWhenIsFreePlanAndNotTrail
      : allAcceptableDropZoneMimeTypes;

  // this var will help to determine the correct api endpoint to request folder creation (If needed).
  const endpointTargetType = dataroomId
    ? `datarooms/${dataroomId}/folders`
    : "folders";

  const onDropRejected = useCallback(
    (rejectedFiles: FileRejection[]) => {
      const rejected = rejectedFiles.map(({ file, errors }) => {
        let message = "";
        if (errors.find(({ code }) => code === "file-too-large")) {
          const fileSizeLimitMB = getFileSizeLimit(file.type, fileSizeLimits);
          message = `File size too big (max. ${fileSizeLimitMB} MB). Upgrade to a paid plan to increase the limit.`;
        } else if (errors.find(({ code }) => code === "file-invalid-type")) {
          const isSupported = SUPPORTED_DOCUMENT_MIME_TYPES.includes(file.type);
          message = `File type not supported ${
            isFree && !isTrial && isSupported ? `on free plan` : ""
          }`;
        }
        return { fileName: file.name, message };
      });
      onUploadRejected(rejected);
    },
    [onUploadRejected, fileSizeLimits, isFree, isTrial],
  );

  const onDrop = useCallback(
    async (acceptedFiles: FileWithPaths[]) => {
      if (!canAddDocuments && acceptedFiles.length > remainingDocuments) {
        toast.error("You have reached the maximum number of documents.");
        return;
      }

      // Validate files and separate into valid and invalid
      const validatedFiles = acceptedFiles.reduce<{
        valid: FileWithPaths[];
        invalid: { fileName: string; message: string }[];
      }>(
        (acc, file) => {
          const fileSizeLimitMB = getFileSizeLimit(file.type, fileSizeLimits);
          const fileSizeLimit = fileSizeLimitMB * 1024 * 1024; // Convert to bytes

          if (file.size > fileSizeLimit) {
            acc.invalid.push({
              fileName: file.name,
              message: `File size too big (max. ${fileSizeLimitMB} MB)${
                isFree && !isTrial
                  ? ". Upgrade to a paid plan to increase the limit"
                  : ""
              }`,
            });
          } else {
            acc.valid.push(file);
          }
          return acc;
        },
        { valid: [], invalid: [] },
      );

      // Handle rejected files first
      if (validatedFiles.invalid.length > 0) {
        setRejectedFiles((prev) => [...validatedFiles.invalid, ...prev]);

        // If all files were rejected, show a summary toast
        if (validatedFiles.valid.length === 0) {
          toast.error(
            `${validatedFiles.invalid.length} file(s) exceeded size limits`,
          );
          return;
        }
      }

      // Continue with valid files
      const newUploads = validatedFiles.valid.map((file) => ({
        fileName: file.name,
        progress: 0,
        uploadId: crypto.randomUUID(),
      }));

      onUploadStart(newUploads);

      const uploadPromises = validatedFiles.valid.map(async (file, index) => {
        // Due to `getFilesFromEvent` file.path will always hold a valid value and represents the value of webkitRelativePath.
        // We no longer need to use webkitRelativePath because everything is been handled in `getFilesFromEvent`
        const path = file.path || file.name;

        // count the number of pages in the file
        let numPages = 1;
        if (file.type === "application/pdf") {
          const buffer = await file.arrayBuffer();
          numPages = await getPagesCount(buffer);

          if (numPages > fileSizeLimits.maxPages) {
            setUploads((prev) =>
              prev.filter((upload) => upload.fileName !== file.name),
            );

            return setRejectedFiles((prev) => [
              {
                fileName: file.name,
                message: `File has too many pages (max. ${fileSizeLimits.maxPages})`,
              },
              ...prev,
            ]);
          }
        }

        const { complete } = await resumableUpload({
          file, // File
          onProgress: (bytesUploaded, bytesTotal) => {
            const progress = Math.min(
              Math.round((bytesUploaded / bytesTotal) * 100),
              99,
            );
            setUploads((prevUploads) =>
              prevUploads.map((upload) =>
                upload.uploadId === newUploads[index].uploadId
                  ? { ...upload, progress }
                  : upload,
              ),
            );

            const _progress = uploadProgress.current.reduce(
              (acc, progress) => acc + progress,
              0,
            );
          },
          onError: (error) => {
            setUploads((prev) =>
              prev.filter(
                (upload) => upload.uploadId !== newUploads[index].uploadId,
              ),
            );

            setRejectedFiles((prev) => [
              { fileName: file.name, message: "Error uploading file" },
              ...prev,
            ]);
          },
          ownerId: (session?.user as CustomUser).id,
          teamId: teamInfo?.currentTeam?.id as string,
          numPages,
          relativePath: path.substring(0, path.lastIndexOf("/")),
        });

        const uploadResult = await complete;

        let contentType = uploadResult.fileType;
        let supportedFileType = getSupportedContentType(contentType) ?? "";

        if (
          uploadResult.fileName.endsWith(".dwg") ||
          uploadResult.fileName.endsWith(".dxf")
        ) {
          supportedFileType = "cad";
          contentType = `image/vnd.${uploadResult.fileName.split(".").pop()}`;
        }

        if (uploadResult.fileName.endsWith(".xlsm")) {
          supportedFileType = "sheet";
          contentType = "application/vnd.ms-excel.sheet.macroEnabled.12";
        }

        if (
          uploadResult.fileName.endsWith(".kml") ||
          uploadResult.fileName.endsWith(".kmz")
        ) {
          supportedFileType = "map";
          contentType = `application/vnd.google-earth.${uploadResult.fileName.endsWith(".kml") ? "kml+xml" : "kmz"}`;
        }

        const documentData: DocumentData = {
          key: uploadResult.id,
          supportedFileType: supportedFileType,
          name: file.name,
          storageType: DocumentStorageType.S3_PATH,
          contentType: contentType,
          fileSize: file.size,
        };

        const fileUploadPathName = file?.whereToUploadPath;

        const response = await createDocument({
          documentData,
          teamId: teamInfo?.currentTeam?.id as string,
          numPages: uploadResult.numPages,
          folderPathName: fileUploadPathName,
        });

        // add the new document to the list
        mutate(`/api/teams/${teamInfo?.currentTeam?.id}/documents`);

        fileUploadPathName &&
          mutate(
            `/api/teams/${teamInfo?.currentTeam?.id}/folders/documents/${fileUploadPathName}`,
          );

        const document = await response.json();
        let dataroomResponse;
        if (dataroomId) {
          try {
            dataroomResponse = await fetch(
              `/api/teams/${teamInfo?.currentTeam?.id}/datarooms/${dataroomId}/documents`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  documentId: document.id,
                  folderPathName: fileUploadPathName,
                }),
              },
            );

            if (!dataroomResponse?.ok) {
              const { message } = await dataroomResponse.json();
              console.error(
                "An error occurred while adding document to the dataroom: ",
                message,
              );
              return;
            }

            mutate(
              `/api/teams/${teamInfo?.currentTeam?.id}/datarooms/${dataroomId}/documents`,
            );
            fileUploadPathName &&
              mutate(
                `/api/teams/${teamInfo?.currentTeam?.id}/datarooms/${dataroomId}/folders/documents/${fileUploadPathName}`,
              );
          } catch (error) {
            console.error(
              "An error occurred while adding document to the dataroom: ",
              error,
            );
          }
        }

        // update progress to 100%
        setUploads((prevUploads) =>
          prevUploads.map((upload) =>
            upload.uploadId === newUploads[index].uploadId
              ? { ...upload, progress: 100, documentId: document.id }
              : upload,
          ),
        );

        analytics.capture("Document Added", {
          documentId: document.id,
          name: document.name,
          numPages: document.numPages,
          path: router.asPath,
          type: document.type,
          contentType: document.contentType,
          teamId: teamInfo?.currentTeam?.id,
          bulkupload: true,
          dataroomId: dataroomId,
          $set: {
            teamId: teamInfo?.currentTeam?.id,
            teamPlan: plan,
          },
        });
        const dataroomDocumentId = dataroomResponse?.ok
          ? (await dataroomResponse.json()).id
          : null;

        return { ...document, dataroomDocumentId: dataroomDocumentId };
      });

      const documents = Promise.all(uploadPromises).finally(() => {
        /* If it a parentFolder was created prior to the upload, we would need to update that
           how many documents and folders does this folder contain rather than displaying 0
            */

        mutate(
          `/api/teams/${teamInfo?.currentTeam?.id}/${endpointTargetType}?root=true`,
        );
        mutate(`/api/teams/${teamInfo?.currentTeam?.id}/${endpointTargetType}`);
        folderPathName &&
          mutate(
            `/api/teams/${teamInfo?.currentTeam?.id}/${endpointTargetType}/${folderPathName}`,
          );
      });
      const uploadedDocuments = await documents;
      const dataroomDocuments = uploadedDocuments.map((document) => ({
        documentId: document.id,
        dataroomDocumentId: document.dataroomDocumentId,
        fileName: document.name,
      }));
      onUploadSuccess?.(dataroomDocuments);
    },
    [
      onUploadStart,
      onUploadProgress,
      endpointTargetType,
      fileSizeLimits,
      isFree,
      isTrial,
    ],
  );

  const getFilesFromEvent = useCallback(
    async (event: DropEvent) => {
      // This callback also run when event.type =`dragenter`. We only need to compute files when the event.type is `drop`.
      if ("type" in event && event.type !== "drop" && event.type !== "change") {
        return [];
      }

      let filesToBePassedToOnDrop: FileWithPaths[] = [];

      /** *********** START OF `traverseFolder` *********** */
      const traverseFolder = async (
        entry: FileSystemEntry,
        parentPathOfThisEntry?: string,
      ): Promise<FileWithPaths[]> => {
        /**
         * Summary of this function:
         *  1. if it find a folder then corresponding folder will be created at backend.
         *  2. Smoothly handles the deeply nested folders.
         *  3. Upon folder creation it assign the path and whereToUploadPath to each entry. (Those values will be helpful for `onDrop` to  upload document correctly)
         */

        let files: FileWithPaths[] = [];

        if (isSystemFile(entry.name)) {
          return files;
        }

        if (entry.isDirectory) {
          /**
           * Let's create the folder.
           * Fact that reader can skip: For Consistency, child files will only be pushed if folder successfully gets created.
           */
          try {
            // An empty folder name can cause the unexpected url problems.
            if (entry.name.trim() === "") {
              setRejectedFiles((prev) => [
                {
                  fileName: entry.name,
                  message: "Folder name cannot be empty",
                },
                ...prev,
              ]);
              throw new Error("Folder name cannot be empty");
            }

            if (!teamInfo?.currentTeam?.id) {
              /** This case probably may not happen */
              setRejectedFiles((prev) => [
                {
                  fileName: "Unknown Team",
                  message: "Team Id not found",
                },
                ...prev,
              ]);
              throw new Error("No team found");
            }

            // Create folder in main documents if not in dataroom
            if (!dataroomId) {
              // Create folder in main documents only
              const { path: folderPath } = await createFolderInMainDocs({
                teamId: teamInfo.currentTeam.id,
                name: entry.name,
                path: parentPathOfThisEntry ?? folderPathName,
              });

              analytics.capture("Folder Added", { folderName: entry.name });

              const dirReader = (
                entry as FileSystemDirectoryEntry
              ).createReader();
              const subEntries = await new Promise<FileSystemEntry[]>(
                (resolve) => dirReader.readEntries(resolve),
              );

              const filteredSubEntries = subEntries.filter(
                (subEntry) => !isSystemFile(subEntry.name),
              );
              for (const subEntry of filteredSubEntries) {
                files.push(
                  ...(await traverseFolder(
                    subEntry,
                    folderPath.startsWith("/")
                      ? folderPath.slice(1)
                      : folderPath,
                  )),
                );
              }
            } else {
              // Create folder in both dataroom and main documents
              const { dataroomPath } = await createFolderInBoth({
                teamId: teamInfo.currentTeam.id,
                dataroomId,
                name: entry.name,
                path: parentPathOfThisEntry ?? folderPathName,
                setRejectedFiles,
                analytics,
              });

              const dirReader = (
                entry as FileSystemDirectoryEntry
              ).createReader();
              const subEntries = await new Promise<FileSystemEntry[]>(
                (resolve) => dirReader.readEntries(resolve),
              );

              const filteredSubEntries = subEntries.filter(
                (subEntry) => !isSystemFile(subEntry.name),
              );

              for (const subEntry of filteredSubEntries) {
                files.push(
                  ...(await traverseFolder(
                    subEntry,
                    dataroomPath.startsWith("/")
                      ? dataroomPath.slice(1)
                      : dataroomPath,
                  )),
                );
              }
            }
          } catch (error) {
            console.error(
              "An error occurred while creating the folder: ",
              error,
            );
            setRejectedFiles((prev) => [
              {
                fileName: entry.name,
                message: "Failed to create the folder",
              },
              ...prev,
            ]);
          }
        } else if (entry.isFile) {
          if (isSystemFile(entry.name)) {
            return files;
          }

          let file = await new Promise<FileWithPaths>((resolve) =>
            (entry as FileSystemFileEntry).file(resolve),
          );

          /** In some browsers e.g firefox is not able to detect the file type. (This only happens when user upload folder) */
          const browserFileTypeCompatibilityIssue = file.type === "";

          if (browserFileTypeCompatibilityIssue) {
            const fileExtension = file.name.split(".").pop()?.toLowerCase();
            let correctMimeType: string | undefined;
            if (fileExtension) {
              // Iterate through acceptableDropZoneFileTypes to find the MIME type for the extension
              for (const [mime, extsUntyped] of Object.entries(
                acceptableDropZoneFileTypes,
              )) {
                const exts = extsUntyped as string[]; // Explicitly type exts
                if (
                  exts.some((ext) => ext.toLowerCase() === "." + fileExtension)
                ) {
                  correctMimeType = mime;
                  break;
                }
              }
            }

            if (correctMimeType) {
              // if we can't do like ```file.type = fileType``` because of [Error: Setting getter-only property "type"]
              // The following is the only best way to resolve the problem
              file = new File([file], file.name, {
                type: correctMimeType,
                lastModified: file.lastModified,
              });
            }
          }

          // Reason of removing "/" because webkitRelativePath doesn't start with "/"
          file.path = entry.fullPath.startsWith("/")
            ? entry.fullPath.substring(1)
            : entry.fullPath;

          file.whereToUploadPath = parentPathOfThisEntry ?? folderPathName;

          files.push(file);
        }

        return files;
      };
      /** *********** END OF `traverseFolder` *********** */

      if ("dataTransfer" in event && event.dataTransfer) {
        const items = event.dataTransfer.items;

        const fileResults = await Promise.all(
          Array.from(items, (item) => {
            // MDN Note: This function is implemented as webkitGetAsEntry() in non-WebKit browsers including Firefox at this time; it may be renamed to getAsEntry() in the future, so you should code defensively, looking for both.
            const entry =
              (typeof item?.webkitGetAsEntry === "function" &&
                item.webkitGetAsEntry()) ??
              (typeof (item as any)?.getAsEntry === "function" &&
                (item as any).getAsEntry()) ??
              null;
            return entry ? traverseFolder(entry) : [];
          }),
        );
        fileResults.forEach((fileResult) =>
          filesToBePassedToOnDrop.push(...fileResult),
        );
      } else if (
        "target" in event &&
        event.target &&
        event.target instanceof HTMLInputElement &&
        event.target.files
      ) {
        for (let i = 0; i < event.target.files.length; i++) {
          const file: FileWithPaths = event.target.files[i];
          file.path = file.name;
          file.whereToUploadPath = folderPathName;
          filesToBePassedToOnDrop.push(event.target.files[i]);
        }
      }

      return filesToBePassedToOnDrop;
    },
    [folderPathName, endpointTargetType, teamInfo],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: acceptableDropZoneFileTypes,
    multiple: true,
    // maxSize: maxSize * 1024 * 1024, // 30 MB
    maxFiles: fileSizeLimits.maxFiles ?? 150,
    onDrop,
    onDropRejected,
    getFilesFromEvent,
  });

  return (
    <div
      {...getRootProps({ onClick: (evt) => evt.stopPropagation() })}
      className={cn(
        "relative",
        dataroomId ? "min-h-[calc(100vh-350px)]" : "min-h-[calc(100vh-270px)]",
      )}
    >
      <div
        className={cn(
          "absolute inset-0 z-40 -m-1 rounded-lg border-2 border-dashed",
          isDragActive
            ? "pointer-events-auto border-primary/50 bg-gray-100/75 backdrop-blur-sm dark:bg-gray-800/75"
            : "pointer-events-none border-none",
        )}
      >
        <input
          {...getInputProps()}
          name="file"
          id="upload-multi-files-zone"
          className="sr-only"
        />

        {isDragActive && (
          <div className="sticky top-1/2 z-50 -translate-y-1/2 px-2">
            <div className="flex justify-center">
              <div className="inline-flex flex-col rounded-lg bg-background/95 px-6 py-4 text-center ring-1 ring-gray-900/5 dark:bg-gray-900/95 dark:ring-white/10">
                <span className="font-medium text-foreground">
                  Drop your file(s) here
                </span>
                <p className="mt-1 text-xs leading-5 text-muted-foreground">
                  {isFree && !isTrial
                    ? `Only *.pdf, *.xls, *.xlsx, *.csv, *.ods, *.png, *.jpeg, *.jpg`
                    : `Only *.pdf, *.pptx, *.docx, *.xlsx, *.xls, *.csv, *.ods, *.ppt, *.odp, *.doc, *.odt, *.dwg, *.dxf, *.png, *.jpg, *.jpeg, *.mp4, *.mov, *.avi, *.webm, *.ogg`}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {children}
    </div>
  );
}
