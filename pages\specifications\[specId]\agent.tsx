import { useRouter } from "next/router";
import { useState, useEffect } from "react";

import { useTeam } from "@/context/team-context";
import { ArrowLeftIcon, BotIcon, SettingsIcon } from "lucide-react";
import useSWR from "swr";
import { toast } from "sonner";

import AppLayout from "@/components/layouts/app";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import LoadingSpinner from "@/components/ui/loading-spinner";

import { AgentChat } from "@/components/api-specifications/agent-chat";
import { convertFunctionToolsToOpenAI } from "@/lib/api-specifications/api-spec-converter";

import { fetcher } from "@/lib/utils";

export default function ApiSpecificationAgentPage() {
  const router = useRouter();
  const { specId } = router.query;
  const teamInfo = useTeam();
  const [agentReady, setAgentReady] = useState(false);

  const teamId = teamInfo?.currentTeam?.id;

  const {
    data: apiSpecification,
    error,
    isLoading,
  } = useSWR(
    teamId && specId ? `/api/teams/${teamId}/api-specifications/${specId}` : null,
    fetcher,
    {
      revalidateOnFocus: false,
    }
  );

  // Get enabled function tools
  const enabledTools = apiSpecification?.functionTools?.filter((tool: any) => tool.isEnabled) || [];
  const openAITools = enabledTools.length > 0 ? convertFunctionToolsToOpenAI(enabledTools) : [];

  useEffect(() => {
    if (apiSpecification && enabledTools.length === 0) {
      toast.error("No enabled function tools found. Redirecting back to specification.");
      router.push(`/specifications/${specId}`);
      return;
    }
    
    if (apiSpecification && enabledTools.length > 0) {
      setAgentReady(true);
    }
  }, [apiSpecification, enabledTools.length, router, specId]);

  if (error) {
    return (
      <AppLayout>
        <div className="flex h-screen items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-semibold">Error loading API specification</h2>
            <p className="text-muted-foreground">
              {error.message || "Something went wrong"}
            </p>
            <Button
              onClick={() => router.push("/specifications")}
              className="mt-4"
            >
              Back to API Specifications
            </Button>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (isLoading || !apiSpecification || !agentReady) {
    return (
      <AppLayout>
        <div className="flex h-screen items-center justify-center">
          <div className="text-center space-y-4">
            <LoadingSpinner className="h-8 w-8 mx-auto" />
            <p className="text-muted-foreground">
              {!agentReady ? "Initializing AI Agent..." : "Loading API specification..."}
            </p>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="flex flex-col h-screen">
        {/* Header */}
        <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center justify-between px-4 py-3">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/specifications/${specId}`)}
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div className="flex items-center gap-2">
                <BotIcon className="h-5 w-5 text-blue-600" />
                <h1 className="text-lg font-semibold">
                  AI Agent - {apiSpecification.name}
                </h1>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {enabledTools.length} tool{enabledTools.length !== 1 ? 's' : ''} enabled
              </Badge>
              {apiSpecification.baseUrl && (
                <Badge variant="outline">
                  {apiSpecification.baseUrl}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Chat Interface */}
        <div className="flex-1 overflow-hidden">
          <AgentChat
            apiSpecification={apiSpecification}
            functionTools={openAITools}
            teamId={teamId!}
          />
        </div>
      </div>
    </AppLayout>
  );
}
