# AI Agent Feature for API Specifications

This document describes the new AI Agent feature that allows users to interact with API specifications through an intelligent conversational interface.

## Overview

The AI Agent feature transforms API specifications into interactive AI agents that can:
- Understand natural language requests about the API
- Execute actual API calls using enabled function tools
- Provide intelligent responses and explanations
- Help users explore and test API endpoints

## Key Components

### 1. Function Tools List UI Update
- **File**: `components/api-specifications/function-tools-list.tsx`
- **Changes**: Replaced "View Parameters" and "View Response" buttons with a single "View Tool Definition" button
- **Purpose**: Shows the OpenAI-formatted function tool definition that will be used by the AI agent

### 2. API Specification Converter
- **File**: `lib/api-specifications/api-spec-converter.ts`
- **Purpose**: Converts API specification endpoints to OpenAI function tool format
- **Features**:
  - Handles parameter mapping (path, query, body, form data)
  - Resolves schema references ($ref)
  - Generates proper JSON Schema for parameters
  - Creates descriptive function names and descriptions

### 3. AI Agent Chat Interface
- **File**: `components/api-specifications/agent-chat.tsx`
- **Purpose**: Provides the chat interface for interacting with the AI agent
- **Features**:
  - Reuses existing chat UI components
  - Handles message flow and state management
  - Integrates with OpenAI API for intelligent responses

### 4. Agent Chat Page
- **File**: `pages/specifications/[specId]/agent.tsx`
- **Purpose**: Full-page interface for the AI agent
- **Features**:
  - Shows enabled function tools count
  - Provides navigation back to specification
  - Initializes the agent with proper context

### 5. API Endpoints
- **Files**: 
  - `pages/api/teams/[teamId]/api-specifications/[id]/agent/chat.ts`
  - `pages/api/teams/[teamId]/api-specifications/[id]/agent/execute.ts`
- **Purpose**: Handle AI agent interactions and function tool execution
- **Features**:
  - OpenAI integration for intelligent responses
  - Function calling support
  - Actual API execution with authentication
  - Error handling and response formatting

## How It Works

### 1. Function Tool Generation
When an API specification is uploaded, the system:
1. Parses the OpenAPI/Swagger specification
2. Extracts endpoints and their parameters
3. Converts them to OpenAI function tool format
4. Stores them as FunctionTool records in the database

### 2. AI Agent Initialization
When a user starts an AI agent:
1. System retrieves enabled function tools for the API specification
2. Converts them to OpenAI function definitions
3. Creates a system prompt with API context
4. Initializes the chat interface

### 3. Conversation Flow
1. User sends a message to the AI agent
2. System sends the message to OpenAI with available function tools
3. OpenAI may respond with text or function calls
4. If function calls are made, system executes actual API requests
5. Results are sent back to OpenAI for interpretation
6. Final response is returned to the user

### 4. Function Tool Execution
When the AI decides to call a function:
1. System looks up the corresponding FunctionTool record
2. Builds the actual HTTP request using the tool's endpoint and method
3. Handles authentication if configured
4. Maps function arguments to API parameters (path, query, body)
5. Executes the HTTP request
6. Returns the response to the AI for interpretation

## Usage

### For Users
1. Navigate to an API specification detail page
2. Ensure at least one function tool is enabled
3. Click the "Start AI Agent" button
4. Interact with the AI using natural language
5. Ask questions about the API or request specific operations

### Example Interactions
- "What endpoints are available in this API?"
- "Get user with ID 123"
- "Create a new user with name John <NAME_EMAIL>"
- "List all products that are available"
- "Update the status of order 456 to shipped"

### For Developers
1. Function tools are automatically generated from API specifications
2. Enable/disable tools in the Function Tools tab
3. Configure API authentication in the specification settings
4. Monitor API calls and responses through the agent interface

## Configuration

### OpenAI API Key
Set the `OPENAI_API_KEY` environment variable to enable full AI functionality. Without it, the agent will work in simulation mode.

### Authentication
Configure API authentication in the API specification settings:
- **API Key**: Header-based API key authentication
- **Bearer Token**: Authorization header with bearer token
- **Basic Auth**: Username/password basic authentication

## Technical Details

### OpenAI Function Tool Format
Each function tool is converted to this format:
```json
{
  "type": "function",
  "function": {
    "name": "operationId",
    "description": "Human-readable description",
    "parameters": {
      "type": "object",
      "properties": { ... },
      "required": [ ... ]
    }
  }
}
```

### Parameter Mapping
- **Path parameters**: `{id}` in URL path → function parameter
- **Query parameters**: URL query string → function parameter
- **Request body**: JSON body → function parameters (flattened or as `body` parameter)
- **Form data**: Form fields → function parameters

### Error Handling
- Network errors are caught and reported
- Authentication failures are handled gracefully
- Invalid function calls are logged and reported to the user
- OpenAI API errors are handled with fallback responses

## Future Enhancements

1. **Streaming Responses**: Implement real-time streaming of AI responses
2. **Function Tool Testing**: Add ability to test function tools directly
3. **Conversation History**: Persist chat history across sessions
4. **Custom Instructions**: Allow users to customize AI agent behavior
5. **API Monitoring**: Track API usage and performance through the agent
6. **Multi-turn Conversations**: Support complex workflows across multiple API calls

## Dependencies

- `@openai/agents`: OpenAI Agents SDK (currently installed but simplified implementation used)
- `openai`: OpenAI API client
- Existing chat UI components from Papermark
- API specification parsing utilities
