import { NextApiRequest, NextApiResponse } from "next";
import { getServerSession } from "next-auth/next";

import { authOptions } from "@/pages/api/auth/[...nextauth]";
import { errorhandler } from "@/lib/errorHandler";
import { CustomUser } from "@/lib/types";
import { getTeamWithUsersAndDocument } from "@/lib/team/helper";
import prisma from "@/lib/prisma";

export default async function handle(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== "POST") {
    res.setHeader("Allow", ["POST"]);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).end("Unauthorized");
  }

  const { teamId, id: apiSpecId } = req.query as { 
    teamId: string; 
    id: string;
  };
  const userId = (session.user as CustomUser).id;

  try {
    // Verify user is part of the team
    await getTeamWithUsersAndDocument({
      teamId,
      userId,
    });
  } catch (error) {
    return errorhandler(error, res);
  }

  const { functionName, arguments: functionArgs } = req.body as {
    functionName: string;
    arguments: any;
  };

  if (!functionName) {
    return res.status(400).json({ error: "Function name is required" });
  }

  try {
    // Get the API specification and function tool
    const apiSpecification = await prisma.apiSpecification.findUnique({
      where: {
        id: apiSpecId,
        teamId: teamId,
      },
      include: {
        functionTools: {
          where: {
            name: functionName,
            isEnabled: true,
          },
        },
      },
    });

    if (!apiSpecification) {
      return res.status(404).json({ error: "API specification not found" });
    }

    const functionTool = apiSpecification.functionTools[0];
    if (!functionTool) {
      return res.status(404).json({ error: "Function tool not found or not enabled" });
    }

    // Execute the API call
    const result = await executeApiCall(
      apiSpecification,
      functionTool,
      functionArgs
    );

    return res.status(200).json(result);
  } catch (error) {
    console.error("Error executing function tool:", error);
    return res.status(500).json({ 
      error: "Failed to execute function tool",
      details: error instanceof Error ? error.message : "Unknown error"
    });
  }
}

/**
 * Execute an API call based on the function tool definition
 */
async function executeApiCall(
  apiSpecification: any,
  functionTool: any,
  args: any
): Promise<any> {
  const { baseUrl } = apiSpecification;
  const { endpoint, method } = functionTool;

  // Build the URL
  let url = endpoint;
  
  // Replace path parameters
  if (args) {
    for (const [key, value] of Object.entries(args)) {
      url = url.replace(`{${key}}`, encodeURIComponent(String(value)));
    }
  }

  // Add base URL if provided
  if (baseUrl) {
    url = baseUrl.replace(/\/$/, '') + '/' + url.replace(/^\//, '');
  }

  // Prepare request options
  const requestOptions: RequestInit = {
    method: method.toUpperCase(),
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'API2Agent/1.0',
    },
  };

  // Handle authentication
  if (apiSpecification.authType && apiSpecification.authConfig) {
    const authHeaders = buildAuthHeaders(
      apiSpecification.authType,
      apiSpecification.authConfig
    );
    Object.assign(requestOptions.headers, authHeaders);
  }

  // Handle query parameters and body
  const urlObj = new URL(url);
  const bodyData: any = {};

  if (args) {
    for (const [key, value] of Object.entries(args)) {
      // Skip path parameters (already replaced)
      if (endpoint.includes(`{${key}}`)) {
        continue;
      }

      // For GET requests, add as query parameters
      if (method.toUpperCase() === 'GET') {
        urlObj.searchParams.append(key, String(value));
      } else {
        // For other methods, add to body
        bodyData[key] = value;
      }
    }
  }

  // Add body for non-GET requests
  if (method.toUpperCase() !== 'GET' && Object.keys(bodyData).length > 0) {
    requestOptions.body = JSON.stringify(bodyData);
  }

  try {
    const response = await fetch(urlObj.toString(), requestOptions);
    
    const responseData = {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      data: null as any,
    };

    // Try to parse response as JSON
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      try {
        responseData.data = await response.json();
      } catch (e) {
        responseData.data = await response.text();
      }
    } else {
      responseData.data = await response.text();
    }

    return responseData;
  } catch (error) {
    throw new Error(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Build authentication headers based on auth type and config
 */
function buildAuthHeaders(authType: string, authConfig: any): Record<string, string> {
  const headers: Record<string, string> = {};

  switch (authType) {
    case 'API_KEY':
      if (authConfig.headerName && authConfig.apiKey) {
        headers[authConfig.headerName] = authConfig.apiKey;
      }
      break;
    
    case 'BEARER_TOKEN':
      if (authConfig.token) {
        headers['Authorization'] = `Bearer ${authConfig.token}`;
      }
      break;
    
    case 'BASIC_AUTH':
      if (authConfig.username && authConfig.password) {
        const credentials = btoa(`${authConfig.username}:${authConfig.password}`);
        headers['Authorization'] = `Basic ${credentials}`;
      }
      break;
    
    default:
      // No authentication or unsupported type
      break;
  }

  return headers;
}
