# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage


# nvm
.npmrc
.nvmrc

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# react-email
.react-email

# Tinybird config for the cli is stored in this file
.tinyb

# Internal scripts
pages/api/scripts
scripts/

# marketing emails
components/emails/marketing
lib/emails/marketing

# vscode configs
.vscode

# trigger.dev
.trigger

# changelog
changelog