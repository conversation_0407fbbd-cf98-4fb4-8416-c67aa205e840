"use client";

import { useState, useEffect, useRef } from "react";
import { toast } from "sonner";

import { ChatInput } from "@/components/chat/chat-input";
import { ChatList } from "@/components/chat/chat-list";
import { ChatScrollAnchor } from "@/components/chat/chat-scroll-anchor";
import { EmptyScreen } from "@/components/chat/empty-screen";

import { OpenAIFunctionTool } from "@/lib/api-specifications/api-spec-converter";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  createdAt?: Date;
}

interface AgentChatProps {
  apiSpecification: any;
  functionTools: OpenAIFunctionTool[];
  teamId: string;
}

export function AgentChat({ apiSpecification, functionTools, teamId }: AgentChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [agent, setAgent] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize the agent
  useEffect(() => {
    if (functionTools.length > 0) {
      // For now, we'll simulate agent initialization
      setAgent({}); // Placeholder

      // Add welcome message
      setMessages([{
        id: "welcome",
        role: "assistant",
        content: `Hello! I'm your AI agent for the ${apiSpecification.name} API. I have access to ${functionTools.length} function tool${functionTools.length !== 1 ? 's' : ''} and can help you interact with the API. What would you like to do?`,
        createdAt: new Date(),
      }]);
    }
  }, [apiSpecification, functionTools]);

  // Execute API call for function tools
  const executeApiCall = async (functionName: string, args: any) => {
    try {
      const response = await fetch(`/api/teams/${teamId}/api-specifications/${apiSpecification.id}/agent/execute`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          functionName,
          arguments: args,
        }),
      });

      if (!response.ok) {
        throw new Error(`API call failed: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("API call execution failed:", error);
      throw error;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!agent || !input.trim() || isLoading) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: "user",
      content: input.trim(),
      createdAt: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    const messageToSend = input.trim();
    setInput("");
    setIsLoading(true);

    try {
      // For now, we'll use a simple approach without the full @openai/agents streaming
      // since the package might have different API than expected
      const response = await fetch(`/api/teams/${teamId}/api-specifications/${apiSpecification.id}/agent/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: messageToSend,
          functionTools: functionTools,
        }),
      });

      if (!response.ok) {
        throw new Error(`Chat request failed: ${response.statusText}`);
      }

      const result = await response.json();

      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        role: "assistant",
        content: result.message || "I received your message but couldn't generate a proper response.",
        createdAt: new Date(),
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Error in agent chat:", error);
      toast.error("Failed to get response from AI agent");

      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        role: "assistant",
        content: "I apologize, but I encountered an error while processing your request. Please try again.",
        createdAt: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement> | string) => {
    if (typeof e === 'string') {
      setInput(e);
    } else {
      setInput(e.target.value);
    }
  };

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Convert messages to the format expected by ChatList
  const chatMessages = messages.map(msg => ({
    id: msg.id,
    role: msg.role,
    content: msg.content,
    createdAt: msg.createdAt,
  }));

  const status = isLoading ? "in_progress" : "awaiting_message";

  // Create example messages for the empty screen
  const exampleMessages = [
    `List all available endpoints in the ${apiSpecification.name} API`,
    `What can I do with this API?`,
    `Show me the API documentation`,
    `Help me make my first API call`,
  ];

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto">
        {messages.length > 0 ? (
          <>
            <ChatList messages={chatMessages} status={status} />
            <ChatScrollAnchor trackVisibility={isLoading} />
          </>
        ) : (
          <div className="flex flex-col h-full">
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center space-y-4 max-w-md">
                <div className="text-4xl">🤖</div>
                <h2 className="text-2xl font-bold">
                  {apiSpecification.name} AI Agent
                </h2>
                <p className="text-muted-foreground">
                  I can help you interact with the {apiSpecification.name} API using {functionTools.length} available function tool{functionTools.length !== 1 ? 's' : ''}.
                  Ask me anything about the API or request specific operations.
                </p>
              </div>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 max-w-2xl mx-auto">
                {exampleMessages.map((message, index) => (
                  <button
                    key={index}
                    onClick={() => handleInputChange(message)}
                    className="p-3 text-left text-sm border rounded-lg hover:bg-muted transition-colors"
                  >
                    {message}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      <div className="border-t bg-background">
        <ChatInput
          status={status}
          error={null}
          messages={chatMessages}
          input={input}
          setInput={setInput}
          submitMessage={handleSubmit}
          handleInputChange={handleInputChange}
        />
      </div>
    </div>
  );
}
