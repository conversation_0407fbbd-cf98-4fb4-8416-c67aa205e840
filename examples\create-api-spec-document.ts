/**
 * Example script showing how to create an API specification
 * using the new hybrid Document + ApiSpecification + FunctionTool model.
 */
import { ApiAuthType, DocumentStorageType } from "@prisma/client";

import { ApiSpecificationData } from "../lib/api/api-specifications/create-api-specification";
import { DocumentData } from "../lib/documents/create-document";

// Step 1: Create a document for the API specification file
const exampleDocumentData: DocumentData = {
  name: "Stripe API v1 Specification",
  key: "stripe-api-spec.json", // This would be the actual file URL/key
  storageType: DocumentStorageType.VERCEL_BLOB,
  contentType: "application/json",
  supportedFileType: "api", // File type for API specifications
  fileSize: 1024 * 50, // 50KB
  numPages: undefined, // API specs don't have pages
  enableExcelAdvancedMode: false,
};

// Step 2: Create API specification data with function tools
const exampleApiSpecData: ApiSpecificationData = {
  name: "Stripe API v1",
  description: "Stripe payment processing API",
  baseUrl: "https://api.stripe.com/v1",
  apiVersion: "2023-10-16",
  authType: ApiAuthType.BEARER_TOKEN,
  authConfig: {
    type: "bearer",
    scheme: "bearer",
    description: "Use your Stripe secret key as the bearer token",
    headerName: "Authorization",
    prefix: "Bearer ",
  },
  functionTools: [
    {
      name: "listCustomers",
      description: "List all customers",
      endpoint: "/customers",
      method: "GET",
      parameters: {
        type: "object",
        properties: {
          limit: {
            type: "integer",
            description: "A limit on the number of objects to be returned",
            default: 10,
          },
          starting_after: {
            type: "string",
            description: "A cursor for use in pagination",
          },
        },
      },
      responseSchema: {
        type: "object",
        properties: {
          object: { type: "string", example: "list" },
          data: {
            type: "array",
            items: { $ref: "#/definitions/Customer" },
          },
          has_more: { type: "boolean" },
          url: { type: "string" },
        },
      },
    },
    {
      name: "createCustomer",
      description: "Create a new customer",
      endpoint: "/customers",
      method: "POST",
      parameters: {
        type: "object",
        required: ["email"],
        properties: {
          email: {
            type: "string",
            description: "Customer's email address",
          },
          name: {
            type: "string",
            description: "Customer's full name",
          },
          description: {
            type: "string",
            description: "An arbitrary string attached to the object",
          },
        },
      },
      responseSchema: {
        $ref: "#/definitions/Customer",
      },
    },
    {
      name: "retrieveCustomer",
      description: "Retrieve a customer by ID",
      endpoint: "/customers/{id}",
      method: "GET",
      parameters: {
        type: "object",
        required: ["id"],
        properties: {
          id: {
            type: "string",
            description: "The customer ID",
          },
        },
      },
      responseSchema: {
        $ref: "#/definitions/Customer",
      },
    },
  ],
};

// Example of different authentication types
const apiKeyAuthExample = {
  authType: ApiAuthType.API_KEY,
  authConfig: {
    type: "apiKey",
    name: "X-API-Key",
    in: "header",
    description: "API key for authentication",
  },
};

const basicAuthExample = {
  authType: ApiAuthType.BASIC_AUTH,
  authConfig: {
    type: "basic",
    description: "HTTP Basic Authentication",
  },
};

const oauth2Example = {
  authType: ApiAuthType.OAUTH2,
  authConfig: {
    type: "oauth2",
    flow: "authorizationCode",
    authorizationUrl: "https://api.example.com/oauth/authorize",
    tokenUrl: "https://api.example.com/oauth/token",
    scopes: {
      read: "Read access to resources",
      write: "Write access to resources",
      admin: "Admin access to all resources",
    },
  },
};

// Function to create an API specification using the hybrid approach
export async function createApiSpecDocument(
  teamId: string,
  documentData: DocumentData,
  apiSpecData: ApiSpecificationData,
  token?: string,
) {
  // Step 1: Create the document first
  const documentResponse = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL}/api/teams/${teamId}/documents`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({
        name: documentData.name,
        url: documentData.key,
        storageType: documentData.storageType,
        type: documentData.supportedFileType,
        contentType: documentData.contentType,
        fileSize: documentData.fileSize,
      }),
    },
  );

  if (!documentResponse.ok) {
    throw new Error(`HTTP error! status: ${documentResponse.status}`);
  }

  const document = await documentResponse.json();

  // Step 2: Create the API specification linked to the document
  const apiSpecResponse = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL}/api/teams/${teamId}/api-specifications`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({
        documentId: document.id,
        ...apiSpecData,
      }),
    },
  );

  if (!apiSpecResponse.ok) {
    throw new Error(`HTTP error! status: ${apiSpecResponse.status}`);
  }

  const apiSpecification = await apiSpecResponse.json();

  return {
    document,
    apiSpecification,
  };
}

// Example usage:
// const result = await createApiSpecDocument(
//   "team_123",
//   exampleDocumentData,
//   exampleApiSpecData
// );
// console.log("Created document:", result.document);
// console.log("Created API specification:", result.apiSpecification);

export {
  exampleDocumentData,
  exampleApiSpecData,
  apiKeyAuthExample,
  basicAuthExample,
  oauth2Example,
};
