import { NextApiRequest, NextApiResponse } from "next";

import { authOptions } from "@/pages/api/auth/[...nextauth]";
import { getServerSession } from "next-auth/next";

import { errorhandler } from "@/lib/errorHandler";
import { openai } from "@/lib/openai";
import prisma from "@/lib/prisma";
import { getTeamWithUsersAndDocument } from "@/lib/team/helper";
import { CustomUser } from "@/lib/types";

export default async function handle(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== "POST") {
    res.setHeader("Allow", ["POST"]);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).end("Unauthorized");
  }

  const { teamId, id: apiSpecId } = req.query as {
    teamId: string;
    id: string;
  };
  const userId = (session.user as CustomUser).id;

  try {
    // Verify user is part of the team
    await getTeamWithUsersAndDocument({
      teamId,
      userId,
    });
  } catch (error) {
    return errorhandler(error, res);
  }

  const { message, functionTools } = req.body as {
    message: string;
    functionTools: any[];
  };

  if (!message) {
    return res.status(400).json({ error: "Message is required" });
  }

  try {
    // Get the API specification
    const apiSpecification = await prisma.apiSpecification.findUnique({
      where: {
        id: apiSpecId,
        teamId: teamId,
      },
      include: {
        functionTools: {
          where: {
            isEnabled: true,
          },
        },
      },
    });

    if (!apiSpecification) {
      return res.status(404).json({ error: "API specification not found" });
    }

    // Create system message with API context
    const systemMessage = `You are an AI assistant that helps users interact with the ${apiSpecification.name} API. 

API Information:
- Name: ${apiSpecification.name}
- Description: ${apiSpecification.description || "No description provided"}
- Base URL: ${apiSpecification.baseUrl || "No base URL specified"}
- Version: ${apiSpecification.apiVersion || "Not specified"}

You have access to the following function tools that can make actual API calls:
${functionTools.map((tool) => `- ${tool.function.name}: ${tool.function.description}`).join("\n")}

When a user asks you to perform an action that requires an API call, you should:
1. Explain what you're going to do
2. Use the appropriate function tool to make the API call
3. Interpret and explain the results to the user

Be helpful, clear, and provide context about what each API call does and what the results mean.`;

    // Convert function tools to OpenAI format
    const tools = functionTools.map((tool) => ({
      type: "function" as const,
      function: {
        name: tool.function.name,
        description: tool.function.description,
        parameters: tool.function.parameters,
      },
    }));

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      return res.status(200).json({
        message: `I'm a simulated AI agent for the ${apiSpecification.name} API. To enable full AI functionality, please configure your OpenAI API key.

Available function tools:
${functionTools.map((tool) => `• ${tool.function.name}: ${tool.function.description}`).join("\n")}

You can test these tools by asking me to perform specific API operations.`,
      });
    }

    // Make the OpenAI API call
    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        { role: "system", content: systemMessage },
        { role: "user", content: message },
      ],
      tools: tools.length > 0 ? tools : undefined,
      tool_choice: tools.length > 0 ? "auto" : undefined,
    });

    const assistantMessage = completion.choices[0]?.message;

    if (!assistantMessage) {
      throw new Error("No response from OpenAI");
    }

    // Handle function calls if any
    if (assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {
      const toolResults = [];

      for (const toolCall of assistantMessage.tool_calls) {
        try {
          // Execute the function call
          const functionName = toolCall.function.name;
          const functionArgs = JSON.parse(toolCall.function.arguments);

          const result = await executeFunctionCall(
            apiSpecification,
            functionName,
            functionArgs,
          );

          toolResults.push({
            tool_call_id: toolCall.id,
            role: "tool" as const,
            content: JSON.stringify(result),
          });
        } catch (error) {
          console.error("Error executing function call:", error);
          toolResults.push({
            tool_call_id: toolCall.id,
            role: "tool" as const,
            content: JSON.stringify({
              error: "Function execution failed",
              details: error instanceof Error ? error.message : "Unknown error",
            }),
          });
        }
      }

      // Get final response with tool results
      const finalCompletion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          { role: "system", content: systemMessage },
          { role: "user", content: message },
          assistantMessage,
          ...toolResults,
        ],
      });

      const finalMessage =
        finalCompletion.choices[0]?.message?.content ||
        "I executed the function but couldn't generate a proper response.";

      return res.status(200).json({
        message: finalMessage,
        toolCalls: assistantMessage.tool_calls,
        toolResults: toolResults,
      });
    }

    // Return the assistant's response
    return res.status(200).json({
      message:
        assistantMessage.content ||
        "I received your message but couldn't generate a proper response.",
    });
  } catch (error) {
    console.error("Error in agent chat:", error);
    return res.status(500).json({
      error: "Failed to process chat message",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
}

/**
 * Execute a function call by making the actual API request
 */
async function executeFunctionCall(
  apiSpecification: any,
  functionName: string,
  args: any,
): Promise<any> {
  // Find the function tool
  const functionTool = apiSpecification.functionTools.find(
    (tool: any) => tool.name === functionName && tool.isEnabled,
  );

  if (!functionTool) {
    throw new Error(`Function tool '${functionName}' not found or not enabled`);
  }

  const { baseUrl } = apiSpecification;
  const { endpoint, method } = functionTool;

  // Build the URL
  let url = endpoint;

  // Replace path parameters
  if (args) {
    for (const [key, value] of Object.entries(args)) {
      url = url.replace(`{${key}}`, encodeURIComponent(String(value)));
    }
  }

  // Add base URL if provided
  if (baseUrl) {
    url = baseUrl.replace(/\/$/, "") + "/" + url.replace(/^\//, "");
  }

  // Prepare request options
  const requestOptions: RequestInit = {
    method: method.toUpperCase(),
    headers: {
      "Content-Type": "application/json",
      "User-Agent": "API2Agent/1.0",
    },
  };

  // Handle authentication
  if (apiSpecification.authType && apiSpecification.authConfig) {
    const authHeaders = buildAuthHeaders(
      apiSpecification.authType,
      apiSpecification.authConfig,
    );
    Object.assign(requestOptions.headers, authHeaders);
  }

  // Handle query parameters and body
  const urlObj = new URL(url);
  const bodyData: any = {};

  if (args) {
    for (const [key, value] of Object.entries(args)) {
      // Skip path parameters (already replaced)
      if (endpoint.includes(`{${key}}`)) {
        continue;
      }

      // For GET requests, add as query parameters
      if (method.toUpperCase() === "GET") {
        urlObj.searchParams.append(key, String(value));
      } else {
        // For other methods, add to body
        bodyData[key] = value;
      }
    }
  }

  // Add body for non-GET requests
  if (method.toUpperCase() !== "GET" && Object.keys(bodyData).length > 0) {
    requestOptions.body = JSON.stringify(bodyData);
  }

  try {
    const response = await fetch(urlObj.toString(), requestOptions);

    const responseData = {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      data: null as any,
    };

    // Try to parse response as JSON
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      try {
        responseData.data = await response.json();
      } catch (e) {
        responseData.data = await response.text();
      }
    } else {
      responseData.data = await response.text();
    }

    return responseData;
  } catch (error) {
    throw new Error(
      `Network error: ${error instanceof Error ? error.message : "Unknown error"}`,
    );
  }
}

/**
 * Build authentication headers based on auth type and config
 */
function buildAuthHeaders(
  authType: string,
  authConfig: any,
): Record<string, string> {
  const headers: Record<string, string> = {};

  switch (authType) {
    case "API_KEY":
      if (authConfig.headerName && authConfig.apiKey) {
        headers[authConfig.headerName] = authConfig.apiKey;
      }
      break;

    case "BEARER_TOKEN":
      if (authConfig.token) {
        headers["Authorization"] = `Bearer ${authConfig.token}`;
      }
      break;

    case "BASIC_AUTH":
      if (authConfig.username && authConfig.password) {
        const credentials = btoa(
          `${authConfig.username}:${authConfig.password}`,
        );
        headers["Authorization"] = `Basic ${credentials}`;
      }
      break;

    default:
      // No authentication or unsupported type
      break;
  }

  return headers;
}
