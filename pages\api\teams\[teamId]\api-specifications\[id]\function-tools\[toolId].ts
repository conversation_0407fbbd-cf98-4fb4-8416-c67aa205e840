import { NextApiRequest, NextApiResponse } from "next";
import { getServerSession } from "next-auth/next";

import { authOptions } from "@/pages/api/auth/[...nextauth]";
import { errorhandler } from "@/lib/errorHandler";
import { CustomUser } from "@/lib/types";
import { getTeamWithUsersAndDocument } from "@/lib/team/helper";
import prisma from "@/lib/prisma";

export default async function handle(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).end("Unauthorized");
  }

  const { teamId, id: apiSpecId, toolId } = req.query as { 
    teamId: string; 
    id: string;
    toolId: string;
  };
  const userId = (session.user as CustomUser).id;

  try {
    // Verify user is part of the team
    await getTeamWithUsersAndDocument({
      teamId,
      userId,
    });
  } catch (error) {
    return errorhandler(error, res);
  }

  if (req.method === "GET") {
    // GET /api/teams/:teamId/api-specifications/:id/function-tools/:toolId
    try {
      const functionTool = await prisma.functionTool.findUnique({
        where: {
          id: toolId,
          apiSpecification: {
            id: apiSpecId,
            teamId: teamId,
          },
        },
        include: {
          apiSpecification: {
            select: {
              id: true,
              name: true,
              teamId: true,
            },
          },
        },
      });

      if (!functionTool) {
        return res.status(404).json({ error: "Function tool not found" });
      }

      return res.status(200).json(functionTool);
    } catch (error) {
      errorhandler(error, res);
    }
  } else if (req.method === "PATCH") {
    // PATCH /api/teams/:teamId/api-specifications/:id/function-tools/:toolId
    const {
      name,
      description,
      endpoint,
      method,
      parameters,
      responseSchema,
      isEnabled,
    } = req.body as Partial<{
      name: string;
      description: string;
      endpoint: string;
      method: string;
      parameters: any;
      responseSchema: any;
      isEnabled: boolean;
    }>;

    try {
      // Verify the function tool exists and belongs to the API specification
      const existingTool = await prisma.functionTool.findUnique({
        where: {
          id: toolId,
          apiSpecification: {
            id: apiSpecId,
            teamId: teamId,
          },
        },
      });

      if (!existingTool) {
        return res.status(404).json({ error: "Function tool not found" });
      }

      // Update the function tool
      const updatedTool = await prisma.functionTool.update({
        where: {
          id: toolId,
        },
        data: {
          ...(name !== undefined && { name }),
          ...(description !== undefined && { description }),
          ...(endpoint !== undefined && { endpoint }),
          ...(method !== undefined && { method }),
          ...(parameters !== undefined && { parameters }),
          ...(responseSchema !== undefined && { responseSchema }),
          ...(isEnabled !== undefined && { isEnabled }),
        },
        include: {
          apiSpecification: {
            select: {
              id: true,
              name: true,
              teamId: true,
            },
          },
        },
      });

      return res.status(200).json(updatedTool);
    } catch (error) {
      errorhandler(error, res);
    }
  } else if (req.method === "DELETE") {
    // DELETE /api/teams/:teamId/api-specifications/:id/function-tools/:toolId
    try {
      // Verify the function tool exists and belongs to the API specification
      const existingTool = await prisma.functionTool.findUnique({
        where: {
          id: toolId,
          apiSpecification: {
            id: apiSpecId,
            teamId: teamId,
          },
        },
      });

      if (!existingTool) {
        return res.status(404).json({ error: "Function tool not found" });
      }

      // Delete the function tool
      await prisma.functionTool.delete({
        where: {
          id: toolId,
        },
      });

      return res.status(200).json({ success: true });
    } catch (error) {
      errorhandler(error, res);
    }
  } else {
    // We only allow GET, PATCH, and DELETE requests
    res.setHeader("Allow", ["GET", "PATCH", "DELETE"]);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
