import { useState, useEffect } from "react";
// @ts-ignore
import { API } from '@stoplight/elements';
import '@stoplight/elements/styles.min.css';

interface StoplightElementsWrapperProps {
  specification: any;
  baseUrl?: string;
}

export default function StoplightElementsWrapper({
  specification,
  baseUrl
}: StoplightElementsWrapperProps) {
  const [StoplightAPI, setStoplightAPI] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadStoplightElements = async () => {
      try {
        setLoading(true);
        setError(null);

        setStoplightAPI(() => API);
        setLoading(false);
      } catch (error) {
        console.error("Error loading Stoplight Elements:", error);
        setError(error instanceof Error ? error.message : "Failed to load Stoplight Elements");
        setLoading(false);
      }
    };

    loadStoplightElements();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64 border rounded-lg">
        <div className="text-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-sm text-muted-foreground">Loading API documentation...</p>
        </div>
      </div>
    );
  }

  if (error || !StoplightAPI) {
    // Fallback to a simple API documentation viewer
    return (
      <div className="w-full">
        <div className="p-6 border rounded-lg">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">API Documentation</h3>
              <span className="text-xs text-muted-foreground">
                {specification?.info?.version && `v${specification.info.version}`}
              </span>
            </div>

            {error && (
              <div className="p-3 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  <strong>Note:</strong> Interactive API documentation is not available. Showing basic information instead.
                </p>
                {error && (
                  <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                    Error: {error}
                  </p>
                )}
              </div>
            )}

            {specification?.info?.description && (
              <p className="text-sm text-muted-foreground">
                {specification.info.description}
              </p>
            )}

            {/* Basic API Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">API Information</h4>
                <div className="text-sm space-y-1">
                  <div><strong>Title:</strong> {specification?.info?.title || 'N/A'}</div>
                  <div><strong>Version:</strong> {specification?.info?.version || 'N/A'}</div>
                  {specification?.host && (
                    <div><strong>Host:</strong> {specification.host}</div>
                  )}
                  {specification?.basePath && (
                    <div><strong>Base Path:</strong> {specification.basePath}</div>
                  )}
                  {specification?.servers && specification.servers.length > 0 && (
                    <div><strong>Server:</strong> {specification.servers[0].url}</div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Available Endpoints</h4>
                <div className="text-sm space-y-1 max-h-40 overflow-y-auto">
                  {specification?.paths && Object.keys(specification.paths).map((path, index) => (
                    <div key={index} className="font-mono text-xs bg-muted p-1 rounded">
                      {path}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Raw specification viewer */}
            <details className="space-y-2">
              <summary className="text-sm font-medium cursor-pointer">
                View Raw Specification
              </summary>
              <pre className="bg-muted p-4 rounded text-xs overflow-auto max-h-96">
                {JSON.stringify(specification, null, 2)}
              </pre>
            </details>
          </div>
        </div>
      </div>
    );
  }

  // Render Stoplight Elements API component
  return (
    <div className="w-full">
      <div className="min-h-[600px] w-full">
        <StoplightAPI
          apiDescriptionDocument={specification}
          basePath="/specifications"
          router="memory"
          hideInternal={true}
          hideSchemas={true}
          hideExport={true}
          hideTryIt={true}
          hideTryItPanel={true}
        />
      </div>
    </div>
  );
}