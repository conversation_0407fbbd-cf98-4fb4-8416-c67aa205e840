import Link from "next/link";
import { useState } from "react";

import { 
  CodeIcon, 
  MoreHorizontalIcon, 
  EyeIcon, 
  EditIcon, 
  TrashIcon,
  CalendarIcon,
  UserIcon,
  GlobeIcon,
  SettingsIcon,
} from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";

import { timeAgo } from "@/lib/utils";

interface ApiSpecification {
  id: string;
  name: string;
  description?: string;
  baseUrl?: string;
  apiVersion?: string;
  authType?: string;
  createdAt: string;
  updatedAt: string;
  document: {
    id: string;
    name: string;
    type?: string;
    contentType?: string;
  };
  _count?: {
    functionTools: number;
  };
  owner?: {
    id: string;
    name?: string;
    email?: string;
  };
}

interface ApiSpecificationsListProps {
  apiSpecifications: ApiSpecification[] | undefined;
  teamInfo: any;
  loading: boolean;
  error: any;
  mutate: () => void;
}

export function ApiSpecificationsList({
  apiSpecifications,
  teamInfo,
  loading,
  error,
  mutate,
}: ApiSpecificationsListProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const handleDelete = async (apiSpecId: string) => {
    if (!confirm("Are you sure you want to delete this API specification?")) {
      return;
    }

    setDeletingId(apiSpecId);
    try {
      const response = await fetch(
        `/api/teams/${teamInfo?.currentTeam?.id}/api-specifications/${apiSpecId}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to delete API specification");
      }

      toast.success("API specification deleted successfully");
      mutate();
    } catch (error) {
      console.error("Error deleting API specification:", error);
      toast.error("Failed to delete API specification");
    } finally {
      setDeletingId(null);
    }
  };

  if (error) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-semibold">Error loading API specifications</h3>
          <p className="text-muted-foreground">
            {error.message || "Something went wrong"}
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-5 w-48" />
                  <Skeleton className="h-4 w-32" />
                </div>
                <Skeleton className="h-8 w-8" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!apiSpecifications || apiSpecifications.length === 0) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center space-y-4">
          <div className="mx-auto h-12 w-12 rounded-full bg-muted flex items-center justify-center">
            <CodeIcon className="h-6 w-6 text-muted-foreground" />
          </div>
          <div>
            <h3 className="text-lg font-semibold">No API specifications</h3>
            <p className="text-muted-foreground">
              Get started by uploading your first API specification
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {apiSpecifications.map((apiSpec) => (
        <Card key={apiSpec.id} className="hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <CardTitle className="flex items-center gap-2">
                  <CodeIcon className="h-5 w-5 text-blue-600" />
                  <Link
                    href={`/specifications/${apiSpec.id}`}
                    className="hover:underline"
                  >
                    {apiSpec.name}
                  </Link>
                </CardTitle>
                {apiSpec.description && (
                  <CardDescription>{apiSpec.description}</CardDescription>
                )}
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontalIcon className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link href={`/specifications/${apiSpec.id}`}>
                      <EyeIcon className="h-4 w-4 mr-2" />
                      View Details
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href={`/specifications/${apiSpec.id}/edit`}>
                      <EditIcon className="h-4 w-4 mr-2" />
                      Edit
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => handleDelete(apiSpec.id)}
                    disabled={deletingId === apiSpec.id}
                    className="text-red-600 focus:text-red-600"
                  >
                    <TrashIcon className="h-4 w-4 mr-2" />
                    {deletingId === apiSpec.id ? "Deleting..." : "Delete"}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>

          <CardContent>
            <div className="space-y-3">
              {/* API Details */}
              <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                {apiSpec.baseUrl && (
                  <div className="flex items-center gap-1">
                    <GlobeIcon className="h-4 w-4" />
                    <span>{apiSpec.baseUrl}</span>
                  </div>
                )}
                {apiSpec.apiVersion && (
                  <Badge variant="secondary">v{apiSpec.apiVersion}</Badge>
                )}
                {apiSpec.authType && (
                  <Badge variant="outline">{apiSpec.authType}</Badge>
                )}
              </div>

              {/* Function Tools Count */}
              {apiSpec._count && (
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <SettingsIcon className="h-4 w-4" />
                  <span>{apiSpec._count.functionTools} function tools</span>
                </div>
              )}

              {/* Metadata */}
              <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
                {apiSpec.owner && (
                  <div className="flex items-center gap-1">
                    <UserIcon className="h-3 w-3" />
                    <span>{apiSpec.owner.name || apiSpec.owner.email}</span>
                  </div>
                )}
                <div className="flex items-center gap-1">
                  <CalendarIcon className="h-3 w-3" />
                  <span>Created {timeAgo(apiSpec.createdAt)}</span>
                </div>
                {apiSpec.updatedAt !== apiSpec.createdAt && (
                  <div className="flex items-center gap-1">
                    <CalendarIcon className="h-3 w-3" />
                    <span>Updated {timeAgo(apiSpec.updatedAt)}</span>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
